<?php
include('./lib/auth.php');
include('lib/translation_helper.php');
date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d H:i:s');

// Get user's basic details
$user_query = dbQuery("
    SELECT u.name, r.name as rank_name
    FROM tabl_user u
    LEFT JOIN tabl_ranks r ON u.current_rank_id = r.id
    WHERE u.id = '$user_id'
");

$user_data = dbFetchAssoc($user_query);
$current_rank_name = $user_data['rank_name'] ?: 'No Rank';
$user_name = $user_data['name'];

// Get daily income history (last 30 days)
$income_history_query = dbQuery("
    SELECT DATE(date) as income_date, amount, date as full_date, order_id
    FROM tabl_walletsummery WHERE user_id = '$user_id'
    AND actiontype = 'daily_rank_income'
    AND type = 'credit'
    AND date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
    ORDER BY date DESC
");

// Get monthly statistics
$monthly_stats_query = dbQuery("
    SELECT
        YEAR(date) as year,
        MONTH(date) as month,
        COUNT(*) as days_received,
        SUM(amount) as total_amount,
        AVG(amount) as avg_amount
    FROM tabl_walletsummery
    WHERE user_id = '$user_id'
    AND actiontype = 'daily_rank_income'
    AND type = 'credit'
    AND date >= DATE_SUB(CURDATE(), INTERVAL 12 MONTH)
    GROUP BY YEAR(date), MONTH(date)
    ORDER BY year DESC, month DESC
    LIMIT 12
");

// Check if income received today
$today_income_query = dbQuery("
    SELECT amount, date
    FROM tabl_walletsummery
    WHERE user_id = '$user_id'
    AND actiontype = 'daily_rank_income'
    AND type = 'credit'
    AND DATE(date) = CURDATE()
    ORDER BY date DESC
    LIMIT 1
");

$today_income = null;
if (dbNumRows($today_income_query) > 0) {
    $today_income = dbFetchAssoc($today_income_query);
}

// Get all ranks for reference
$all_ranks_query = dbQuery("SELECT * FROM tabl_ranks ORDER BY id ASC");

// Calculate total income received
$total_income_query = dbQuery("
    SELECT SUM(amount) as total_received, COUNT(*) as total_days
    FROM tabl_walletsummery
    WHERE user_id = '$user_id'
    AND actiontype = 'daily_rank_income'
    AND type = 'credit'
");
$total_income_data = dbFetchAssoc($total_income_query);
$total_received = $total_income_data['total_received'] ?: 0;
$total_days = $total_income_data['total_days'] ?: 0;
?>

<!doctype html>
<html class="no-js" lang="">

<head>
    <base href="<?= BASE_URL; ?>">

    <meta charset="utf-8">
    <meta http-equiv="x-ua-compatible" content="ie=edge">
    <title>Daily Income - Win TV 99</title>
    <meta name="description"
        content="View your daily income and earnings from rank achievements">
    <meta name="keywords" content="daily income,earnings,rank,income history">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!-- Favicon -->
    <link rel="shortcut icon" type="image/x-icon" href="img/favicon2.png">
    <!-- Normalize CSS -->
    <link rel="stylesheet" href="css/normalize.css">
    <!-- Main CSS -->
    <link rel="stylesheet" href="css/main.css">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" rel="stylesheet"
        integrity="sha384-EVSTQN3/azprG1Anm3QDgpJLIm9Nao0Yz1ztcQTwFspd3yD65VohhpuuCOmLASjC" crossorigin="anonymous">
    <link rel="stylesheet" href="css/bootstrap.min.css">

    <!-- Animate CSS -->
    <link rel="stylesheet" href="css/animate.min.css">
    <!-- Font-awesome CSS-->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="css/font-awesome.min.css">
    <!-- Owl Caousel CSS -->
    <link rel="stylesheet" href="vendor/OwlCarousel/owl.carousel.min.css">
    <link rel="stylesheet" href="vendor/OwlCarousel/owl.theme.default.min.css">
    <!-- Main Menu CSS -->
    <link rel="stylesheet" href="css/meanmenu.min.css">
    <!-- Nivo Slider CSS-->
    <link rel="stylesheet" href="vendor/slider/css/nivo-slider.css" type="text/css" />
    <link rel="stylesheet" href="vendor/slider/css/preview.css" type="text/css" media="screen" />
    <!-- Magnific CSS -->
    <link rel="stylesheet" type="text/css" href="css/magnific-popup.css">
    <!-- Switch Style CSS -->
    <link rel="stylesheet" href="css/hover-min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="style.css">
    <!-- For IE -->
    <link rel="stylesheet" type="text/css" href="css/ie-only.css" />
    <link rel="stylesheet" href="css/me.css">
    <link rel="stylesheet" href="css/custom.css">
    <link rel="stylesheet" href="css/lightfooter.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <!-- Modernizr Js -->
    <script src="js/modernizr-2.8.3.min.js"></script>

    <style>
        .income-summary-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .income-amount {
            font-size: 2.5em;
            font-weight: bold;
        }
        .breakdown-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 10px;
            margin: 5px 0;
        }
        .stats-card {
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
            text-align: center;
        }
    </style>

</head>

<body>
    <div id="main">

        <div id="wrapper">
            <?php include("./inc/header_mobile.php"); ?>

            <?php include("./inc/header.php"); ?>

            <div class="d-lg-none d-block" style="padding-top: 50px;"> </div>
            <div class="d-none d-lg-block" style="padding-top: 130px;"> </div>

            <section class="container mt-5">
                <h1 class="cat-heading text-left">Daily Income</h1>
            </section>

            <section class="section-space-bottom mt-5">
                <div class="container mt-4">
                    <div class="row">
                        <!-- Sidebar Menu -->
                        <?php include_once "./inc/sidebar.php"; ?>

                        <!-- Main Daily Income Content -->
                        <div class="col-md-9">
                            <!-- Income Summary Card -->
                            <div class="card shadow-sm border-0 mb-4">
                                <div class="card-header bg-light">
                                    <h2 class="mb-0">Daily Income Summary</h2>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="income-summary-card">
                                                <h4><i class="fas fa-coins"></i> Daily Income Overview</h4>
                                                <p class="mb-2">Current rank: <strong><?= htmlspecialchars($current_rank_name) ?></strong></p>
                                                <small>View your daily income transaction history below</small>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="row">
                                                <div class="col-12 mb-3">
                                                    <div class="stats-card">
                                                        <h5>Today's Income</h5>
                                                        <?php if ($today_income): ?>
                                                            <h3 class="text-success">₹<?= number_format($today_income['amount'], 2) ?></h3>
                                                            <small>Received at <?= date('h:i A', strtotime($today_income['date'])) ?></small>
                                                        <?php else: ?>
                                                            <h3 class="text-muted">₹0.00</h3>
                                                            <small>Not received yet</small>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                                <div class="col-12">
                                                    <div class="stats-card">
                                                        <h5>Total Received</h5>
                                                        <h3 class="text-primary">₹<?= number_format($total_received, 2) ?></h3>
                                                        <small><?= $total_days ?> days received</small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>



                            <!-- Daily Income History -->
                            <div class="card shadow-sm border-0 mb-4">
                                <div class="card-header bg-light">
                                    <h2 class="mb-0">Daily Income History</h2>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-bordered table-striped">
                                            <thead class="table-dark">
                                                <tr>
                                                    <th>#</th>
                                                    <th>Order ID</th>
                                                    <th>Amount</th>
                                                    <th>Type</th>
                                                    <th>Action</th>
                                                    <th>Date</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php
                                                $i = 1;
                                                if (dbNumRows($income_history_query) > 0) {
                                                    while ($row = dbFetchAssoc($income_history_query)) {
                                                        $badgeClass = 'bg-success'; // Daily income is always credit
                                                        $formattedDate = date('d M Y, h:i A', strtotime($row['full_date']));
                                                        echo "<tr>
                                                        <td>{$i}</td>
                                                        <td>" . htmlspecialchars($row['order_id'] ?: '-') . "</td>
                                                        <td>₹" . number_format($row['amount'], 2) . "</td>
                                                        <td><span class='badge {$badgeClass}'>Credit</span></td>
                                                        <td>Daily Rank Income</td>
                                                        <td>{$formattedDate}</td>
                                                    </tr>";
                                                        $i++;
                                                    }
                                                } else {
                                                    echo "<tr><td colspan='6' class='text-center'>No daily income history found.</td></tr>";
                                                }
                                                ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>


                        </div>
                    </div>
                </div>


            </section>

            <?php include("./inc/footer_main.php"); ?>


        </div>


        <!-- jquery-->
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/js/bootstrap.bundle.min.js"
            integrity="sha384-MrcW6ZMFYlzcLA8Nl+NtUVF0sA7MsXsP1UyJoMp4YLEuNSfAP+JcXn/tWtIaxVXM"
            crossorigin="anonymous"></script>
        <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.2/dist/umd/popper.min.js"
            integrity="sha384-IQsoLXl5PILFhosVNubq5LC7Qb9DXgDA9i+tQ8Zj3iwWAwPtgFTxbJ8NT4GN1R8p"
            crossorigin="anonymous"></script>
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/js/bootstrap.min.js"
            integrity="sha384-cVKIPhGWiC2Al4u+LWgxfKTRIcfu0JTxR+EQDz/bgldoEyl4H0zUF0QKbrJ0EcQF"
            crossorigin="anonymous"></script>
        <script src="js/jquery-2.2.4.min.js" type="text/javascript"></script>
        <!-- Plugins js -->
        <script src="js/plugins.js" type="text/javascript"></script>
        <!-- Popper js -->
        <script src="js/popper.js" type="text/javascript"></script>
        <!-- Bootstrap js -->
        <script src="js/bootstrap.min.js" type="text/javascript"></script>
        <!-- WOW JS -->
        <script src="js/wow.min.js"></script>
        <!-- Owl Cauosel JS -->
        <script src="vendor/OwlCarousel/owl.carousel.min.js" type="text/javascript"></script>
        <!-- Meanmenu Js -->
        <script src="js/jquery.meanmenu.min.js" type="text/javascript"></script>
        <!-- Srollup js -->
        <script src="js/jquery.scrollUp.min.js" type="text/javascript"></script>
        <!-- jquery.counterup js -->
        <script src="js/jquery.counterup.min.js"></script>
        <script src="js/waypoints.min.js"></script>
        <!-- Nivo slider js -->
        <script src="vendor/slider/js/jquery.nivo.slider.js" type="text/javascript"></script>
        <script src="vendor/slider/home.js" type="text/javascript"></script>
        <!-- Isotope js -->
        <script src="js/isotope.pkgd.min.js" type="text/javascript"></script>
        <!-- Magnific Popup -->
        <script src="js/jquery.magnific-popup.min.js"></script>
        <!-- Ticker Js -->
        <script src="js/ticker.js" type="text/javascript"></script>
        <!-- Custom Js -->
        <script src="js/main.js" type="text/javascript"></script>

        <script>
            function copyTextToClipboard(owncode) {

                var link = "<?= BASE_URL; ?>signup.php?invite_code=" + owncode;

                navigator.clipboard.writeText(link).then(() => {
                    alert("Referral code copied to clipboard!");
                });
            }
        </script>

        <script>
            document.addEventListener('DOMContentLoaded', function() {
                const accordionItems = document.querySelectorAll('.accordion-item');

                accordionItems.forEach(function(item) {
                    const title = item.querySelector('.accordion-title');

                    title.addEventListener('click', function() {
                        const content = this.nextElementSibling;

                        if (content.style.display === 'block') {
                            content.style.display = 'none';
                            this.classList.remove('active');
                        } else {
                            content.style.display = 'block';
                            this.classList.add('active');
                        }
                    });
                });
            });



            //         function toggleAdditionalLinks() {
            //             var additionalLinks = document.getElementById("additionalLinks");



            //             if (additionalLinks.style.display === "none") {
            //                 additionalLinks.style.display = "block";
            //             } else {
            //                 additionalLinks.style.display = "none";
            //             }
            //  }


            // here another js
            function toggleAdditionalLinks(event) {
                var additionalLinks = document.getElementById("additionalLinks");
                var additionalLinkstour = document.getElementById("additionalLinks-tour");
                var additionalLinksstay = document.getElementById("additionalLinks-stay");

                // Prevent the click event from propagating to the document
                event.stopPropagation();

                // Hide other sections
                if (additionalLinkstour.style.display === "block") {
                    additionalLinkstour.style.display = "none";
                    document.removeEventListener('click', hideAdditionalLinkstourOnClickOutside);
                }

                if (additionalLinksstay.style.display === "block") {
                    additionalLinksstay.style.display = "none";
                    document.removeEventListener('click', hideAdditionalLinksstayOnClickOutside);
                }

                if (additionalLinks.style.display === "none") {
                    additionalLinks.style.display = "block";
                    document.addEventListener('click', hideAdditionalLinksOnClickOutside);
                } else {
                    additionalLinks.style.display = "none";
                    document.removeEventListener('click', hideAdditionalLinksOnClickOutside);
                }
            }

            function hideAdditionalLinksOnClickOutside(event) {
                var additionalLinks = document.getElementById("additionalLinks");
                if (!additionalLinks.contains(event.target) && event.target.id !== 'toggleLink') {
                    additionalLinks.style.display = "none";
                    document.removeEventListener('click', hideAdditionalLinksOnClickOutside);
                }
            }

            function toggleAdditionalLinkstour(event) {
                var additionalLinkstour = document.getElementById("additionalLinks-tour");
                var additionalLinks = document.getElementById("additionalLinks");
                var additionalLinksstay = document.getElementById("additionalLinks-stay");

                // Prevent the click event from propagating to the document
                event.stopPropagation();

                // Hide other sections
                if (additionalLinks.style.display === "block") {
                    additionalLinks.style.display = "none";
                    document.removeEventListener('click', hideAdditionalLinksOnClickOutside);
                }

                if (additionalLinksstay.style.display === "block") {
                    additionalLinksstay.style.display = "none";
                    document.removeEventListener('click', hideAdditionalLinksstayOnClickOutside);
                }

                if (additionalLinkstour.style.display === "none") {
                    additionalLinkstour.style.display = "block";
                    document.addEventListener('click', hideAdditionalLinkstourOnClickOutside);
                } else {
                    additionalLinkstour.style.display = "none";
                    document.removeEventListener('click', hideAdditionalLinkstourOnClickOutside);
                }
            }

            function hideAdditionalLinkstourOnClickOutside(event) {
                var additionalLinkstour = document.getElementById("additionalLinks-tour");
                if (!additionalLinkstour.contains(event.target) && event.target.id !== 'toggleLinkTour') {
                    additionalLinkstour.style.display = "none";
                    document.removeEventListener('click', hideAdditionalLinkstourOnClickOutside);
                }
            }

            function toggleAdditionalLinksstay(event) {
                var additionalLinksstay = document.getElementById("additionalLinks-stay");
                var additionalLinks = document.getElementById("additionalLinks");
                var additionalLinkstour = document.getElementById("additionalLinks-tour");

                // Prevent the click event from propagating to the document
                event.stopPropagation();

                // Hide other sections
                if (additionalLinks.style.display === "block") {
                    additionalLinks.style.display = "none";
                    document.removeEventListener('click', hideAdditionalLinksOnClickOutside);
                }

                if (additionalLinkstour.style.display === "block") {
                    additionalLinkstour.style.display = "none";
                    document.removeEventListener('click', hideAdditionalLinkstourOnClickOutside);
                }

                if (additionalLinksstay.style.display === "none") {
                    additionalLinksstay.style.display = "block";
                    document.addEventListener('click', hideAdditionalLinksstayOnClickOutside);
                } else {
                    additionalLinksstay.style.display = "none";
                    document.removeEventListener('click', hideAdditionalLinksstayOnClickOutside);
                }
            }

            function hideAdditionalLinksstayOnClickOutside(event) {
                var additionalLinksstay = document.getElementById("additionalLinks-stay");
                if (!additionalLinksstay.contains(event.target) && event.target.id !== 'toggleLinkStay') {
                    additionalLinksstay.style.display = "none";
                    document.removeEventListener('click', hideAdditionalLinksstayOnClickOutside);
                }
            }

            document.getElementById("toggleLink").addEventListener("click", toggleAdditionalLinks);
            document.getElementById("toggleLinkTour").addEventListener("click", toggleAdditionalLinkstour);
            document.getElementById("toggleLinkStay").addEventListener("click", toggleAdditionalLinksstay);











            // Detect scroll event
            window.onscroll = function() {
                scrollFunction()
            };

            function scrollFunction() {
                var footer = document.querySelector('.mobile-footer');
                if (document.body.scrollTop > 20 || document.documentElement.scrollTop > 20) {
                    footer.classList.add('show'); // Add class to show footer
                } else {
                    footer.classList.remove('show'); // Remove class to hide footer
                }
            }
        </script>
        <script>
            function toggleSection(headerElement) {
                const content = headerElement.nextElementSibling;
                const icon = headerElement.querySelector('.icon i');

                if (content.style.display === "block") {
                    content.style.display = "none";
                    icon.classList.remove('fa-chevron-down');
                    icon.classList.add('fa-chevron-up');
                } else {
                    content.style.display = "block";
                    icon.classList.remove('fa-chevron-up');
                    icon.classList.add('fa-chevron-down');
                }
            }


            $(document).ready(function() {
                $('#carouselExampleControls').carousel({
                    interval: 2000 // 2 seconds
                });
            });

            $(document).ready(function() {
                $('#menuIcon').on('click', function() {
                    $(this).toggleClass('bi-list bi-x');
                });

                $('#offcanvasExample').on('hidden.bs.offcanvas', function() {
                    $('#menuIcon').removeClass('bi-x').addClass('bi-list');
                });
            });


            $(document).ready(function() {
                $('#smenuIcon').on('click', function() {
                    $(this).toggleClass('bi-list bi-x');
                });

                $('#offcanvasExample').on('hidden.bs.offcanvas', function() {
                    $('#smenuIcon').removeClass('bi-x').addClass('bi-list');
                });
            });
        </script>
</body>


</html>
