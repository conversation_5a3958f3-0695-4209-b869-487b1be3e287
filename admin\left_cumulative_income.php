<?php
session_start();
include('lib/db_connection.php');
include('lib/get_functions.php');
include('lib/auth.php');
date_default_timezone_set("Asia/Kolkata");

// Set page variables for menu highlighting
$page = 10;
$sub_page = 105;


?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1">
    <meta http-equiv="x-ua-compatible" content="ie=edge">
    <title><?php echo SITE; ?> | Left Cumulative Income Management</title>
    <link href="favicon.png" rel="shortcut icon">
    <link href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:400,400i,700,700i,900" rel="stylesheet">
    <!-- VENDORS -->
    <link rel="stylesheet" type="text/css" href="vendors/bootstrap/dist/css/bootstrap.css">
    <link rel="stylesheet" type="text/css" href="vendors/font-feathericons/dist/feather.css">
    <link rel="stylesheet" type="text/css" href="vendors/font-awesome/css/font-awesome.min.css">
    <link rel="stylesheet" type="text/css" href="vendors/font-linearicons/style.css">
    <link rel="stylesheet" type="text/css" href="vendors/font-icomoon/style.css">
    <link rel="stylesheet" type="text/css" href="vendors/perfect-scrollbar/css/perfect-scrollbar.css">
    <link rel="stylesheet" type="text/css" href="vendors/bootstrap-sweetalert/dist/sweetalert.css">
    <script src="vendors/jquery/dist/jquery.min.js"></script>
    <script src="vendors/popper.js/dist/umd/popper.js"></script>
    <script src="vendors/bootstrap/dist/js/bootstrap.js"></script>
    <script src="vendors/perfect-scrollbar/js/perfect-scrollbar.jquery.js"></script>
    <script src="vendors/bootstrap-sweetalert/dist/sweetalert.min.js"></script>

    <!-- AIR UI HTML ADMIN TEMPLATE MODULES-->
    <link rel="stylesheet" type="text/css" href="components/vendors/style.css">
    <link rel="stylesheet" type="text/css" href="components/core/style.css">
    <link rel="stylesheet" type="text/css" href="components/widgets/style.css">
    <link rel="stylesheet" type="text/css" href="components/system/style.css">
    <link rel="stylesheet" type="text/css" href="components/menu-left/style.css">
    <link rel="stylesheet" type="text/css" href="components/menu-top/style.css">
    <link rel="stylesheet" type="text/css" href="components/footer/style.css">
    <link rel="stylesheet" type="text/css" href="components/topbar/style.css">
    <link rel="stylesheet" type="text/css" href="components/subbar/style.css">
    <link rel="stylesheet" type="text/css" href="components/sidebar/style.css">
    <script src="components/core/index.js"></script>
    <script src="components/menu-left/index.js"></script>
    <script src="components/menu-top/index.js"></script>
    <script src="components/sidebar/index.js"></script>
    <script src="components/topbar/index.js"></script>

    <style>
        .income-card {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            background: #f8f9fa;
        }

        .rank-card {
            border: 1px solid #007bff;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            background: #e3f2fd;
        }

        .left-amount {
            font-size: 1.2em;
            font-weight: bold;
            color: #28a745;
        }

        .search-section {
            background: #ffffff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .badge-success {
            background-color: #28a745;
            color: white;
        }

        .badge-danger {
            background-color: #dc3545;
            color: white;
        }

        .alert-success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }

        .alert-danger {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
    </style>
</head>

<body class="air__menu--blue air__menu__submenu--blue">
    <div class="air__initialLoading"></div>
    <div class="air__layout">
        <div class="air__menuTop">
            <div class="air__menuTop__outer">
                <div class="air__menuTop__mobileToggleButton air__menuTop__mobileActionToggle"> <span></span> </div>
                <a href="home.php" class="air__menuTop__logo">
                    <h1 style="color:#FFF"><?php echo SITE; ?></h1>
                </a>
                <?php include('inc/__menu.php'); ?>
            </div>
        </div>
        <div class="air__menuTop__backdrop air__menuTop__mobileActionToggle"></div>
        <div class="air__layout">
            <?php include('inc/__header.php'); ?>
            <div class="air__layout__content">
                <div class="air__utils__content">
                    <div class="air__utils__heading">
                        <h5>Left Cumulative Income Management</h5>
                        <nav aria-label="breadcrumb" style="float: right;margin-top: -35px;">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="home.php">Home</a></li>
                                <li class="breadcrumb-item"><a href="packages.php">Investment</a></li>
                                <li class="breadcrumb-item active" aria-current="page">Left Cumulative Income</li>
                            </ol>
                        </nav>
                    </div>
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="card">
                                <div class="card-body">

                                    <!-- Search Section -->
                                    <div class="row mb-3">
                                        <div class="col-md-4">
                                            <label for="user_id">Search User by ID:</label>
                                            <input type="number" id="user_id" class="form-control" placeholder="Enter User ID">
                                        </div>
                                        <div class="col-md-2 d-flex align-items-end">
                                            <button onclick="loadUserLeftIncome()" class="btn btn-primary">
                                                <i class="fa fa-search"></i> Load Data
                                            </button>
                                        </div>
                                        <div class="col-md-6 d-flex align-items-end">
                                            <div id="user-info" class="text-info"></div>
                                        </div>
                                    </div>

                                    <!-- Data Table -->
                                    <div class="row">
                                        <div class="col-lg-12">
                                            <div class="mb-5">
                                                <div id="example1_wrapper" class="dataTables_wrapper dt-bootstrap4 no-footer">
                                                    <div class="row">
                                                        <div class="col-sm-12">
                                                            <table class="table table-hover nowrap dataTable dtr-inline no-footer" id="leftIncomeTable">
                                                                <thead>
                                                                    <tr>
                                                                        <th>Rank</th>
                                                                        <th>Daily Credit</th>
                                                                        <th>Days Received</th>
                                                                        <th>Left Days</th>
                                                                        <th>Calculated Amount</th>
                                                                        <th>Final Amount</th>
                                                                        <th>Action</th>
                                                                    </tr>
                                                                </thead>
                                                                <tbody id="leftIncomeTableBody">
                                                                    <tr>
                                                                        <td colspan="7" class="text-center text-muted">
                                                                            <i class="fa fa-search"></i> Search for a user to view left cumulative income data
                                                                        </td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Bulk Actions -->
                                    <div class="row" id="bulkActions" style="display: none;">
                                        <div class="col-lg-12">
                                            <div class="card">
                                                <div class="card-header bg-warning">
                                                    <h6><i class="fa fa-money"></i> Bulk Distribution</h6>
                                                </div>
                                                <div class="card-body">
                                                    <p>Distribute all left cumulative income at once:</p>
                                                    <button onclick="distributeAllLeftIncome()" class="btn btn-warning btn-lg">
                                                        <i class="fa fa-money"></i> Distribute All Left Income
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php include('inc/__footer.php'); ?>
        </div>
    </div>
</body>

</html>
<script>
    let currentUserId = null;
    let currentLeftIncomeData = [];

    function loadUserLeftIncome() {
        const userId = document.getElementById('user_id').value;

        if (!userId) {
            alert('Please enter a User ID');
            return;
        }

        currentUserId = userId;

        // Clear previous data
        document.getElementById('leftIncomeTableBody').innerHTML = `
                <tr>
                    <td colspan="7" class="text-center">
                        <i class="fa fa-spinner fa-spin"></i> Loading...
                    </td>
                </tr>
            `;

        // Get user left income data
        fetch('left_cumulative_income.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=get_user_left_income&user_id=${userId}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    displayUserInfo(data.user);
                    displayLeftIncomeTable(data.left_income_data);
                    currentLeftIncomeData = data.left_income_data;
                } else {
                    document.getElementById('leftIncomeTableBody').innerHTML = `
                        <tr>
                            <td colspan="7" class="text-center text-danger">
                                <i class="fa fa-exclamation-triangle"></i> ${data.message}
                            </td>
                        </tr>
                    `;
                    document.getElementById('user-info').innerHTML = '';
                    document.getElementById('bulkActions').style.display = 'none';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                document.getElementById('leftIncomeTableBody').innerHTML = `
                    <tr>
                        <td colspan="7" class="text-center text-danger">
                            <i class="fa fa-exclamation-triangle"></i> Error loading data
                        </td>
                    </tr>
                `;
            });
    }

    function displayUserInfo(user) {
        const userInfo = `
                <strong>${user.name}</strong> (${user.email}) |
                Current Rank: <span class="badge badge-primary">${user.current_rank_name || 'No Rank'}</span> |
                Registered: ${user.registration_date}
            `;
        document.getElementById('user-info').innerHTML = userInfo;
    }

    function displayLeftIncomeTable(leftIncomeData) {
        let tableRows = '';
        let hasLeftIncome = false;

        if (leftIncomeData.length === 0) {
            tableRows = `
                    <tr>
                        <td colspan="7" class="text-center text-muted">
                            <i class="fa fa-info-circle"></i> No previous ranks found for this user
                        </td>
                    </tr>
                `;
        } else {
            leftIncomeData.forEach((rank, index) => {
                if (rank.updated_amount > 0) {
                    hasLeftIncome = true;
                }

                const isEdited = rank.updated_amount !== rank.left_amount;

                tableRows += `
                        <tr>
                            <td>
                                <strong>${rank.rank_name}</strong><br>
                                <small class="text-muted">Rank ${rank.rank_id}</small>
                            </td>
                            <td>₹${rank.daily_credit}</td>
                            <td>
                                ${rank.received_days} / ${rank.total_possible_days}<br>
                                <small class="text-muted">Already given: ${rank.left_received_days}</small>
                            </td>
                            <td>${rank.left_days}</td>
                            <td>
                                ₹${rank.left_amount}
                                ${isEdited ? '<br><small class="text-muted">(Original)</small>' : ''}
                            </td>
                            <td>
                                <input type="number"
                                       class="form-control form-control-sm"
                                       value="${rank.updated_amount}"
                                       onchange="updateAmount(${rank.rank_id}, this.value)"
                                       style="width: 120px; ${isEdited ? 'background-color: #fff3cd;' : ''}"
                                       min="0"
                                       step="0.01">
                                ${isEdited ? '<br><small class="text-warning">Modified</small>' : ''}
                            </td>
                            <td>
                                ${rank.updated_amount > 0 ? `
                                    <button onclick="distributeIncome(${rank.rank_id}, ${rank.updated_amount}, '${rank.rank_name}')"
                                            class="btn btn-success btn-sm">
                                        <i class="fa fa-money"></i> Distribute
                                    </button>
                                ` : `
                                    <span class="text-muted">No amount</span>
                                `}
                            </td>
                        </tr>
                    `;
            });
        }

        document.getElementById('leftIncomeTableBody').innerHTML = tableRows;

        // Show/hide bulk actions
        if (hasLeftIncome) {
            document.getElementById('bulkActions').style.display = 'block';
        } else {
            document.getElementById('bulkActions').style.display = 'none';
        }
    }

    function updateAmount(rankId, newAmount) {
        if (!currentUserId) {
            alert('No user selected');
            return;
        }

        fetch('left_cumulative_income.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=update_left_amount&user_id=${currentUserId}&rank_id=${rankId}&new_amount=${newAmount}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    // Update the current data
                    const rankIndex = currentLeftIncomeData.findIndex(r => r.rank_id == rankId);
                    if (rankIndex !== -1) {
                        currentLeftIncomeData[rankIndex].updated_amount = parseFloat(newAmount);
                    }
                    // Refresh the table display
                    displayLeftIncomeTable(currentLeftIncomeData);
                } else {
                    alert('Error updating amount: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error updating amount');
            });
    }

    function distributeIncome(rankId, amount, rankName) {
        if (!currentUserId) {
            alert('No user selected');
            return;
        }

        if (!confirm(`Are you sure you want to distribute ₹${amount} from ${rankName} rank?`)) {
            return;
        }

        fetch('left_cumulative_income.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=distribute_left_income&user_id=${currentUserId}&rank_id=${rankId}&amount=${amount}&days=1`
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    alert(data.message);
                    // Refresh the data
                    loadUserLeftIncome();
                } else {
                    alert(data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while distributing income');
            });
    }

    function distributeAllLeftIncome() {
        if (!currentUserId || !currentLeftIncomeData) {
            alert('No user data available');
            return;
        }

        const ranksWithLeftIncome = currentLeftIncomeData.filter(rank => rank.updated_amount > 0);

        if (ranksWithLeftIncome.length === 0) {
            alert('No left income to distribute');
            return;
        }

        const totalAmount = ranksWithLeftIncome.reduce((sum, rank) => sum + rank.updated_amount, 0);
        const ranksList = ranksWithLeftIncome.map(rank => `${rank.rank_name}: ₹${rank.updated_amount}`).join('\n');

        if (!confirm(`Are you sure you want to distribute all left income?\n\nTotal Amount: ₹${totalAmount}\n\nBreakdown:\n${ranksList}`)) {
            return;
        }

        // Distribute each rank's left income sequentially
        let distributionPromises = ranksWithLeftIncome.map(rank => {
            return fetch('left_cumulative_income.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=distribute_left_income&user_id=${currentUserId}&rank_id=${rank.rank_id}&amount=${rank.updated_amount}&days=1`
            }).then(response => response.json());
        });

        Promise.all(distributionPromises)
            .then(results => {
                const successful = results.filter(result => result.status === 'success').length;
                const failed = results.filter(result => result.status === 'error').length;

                if (failed === 0) {
                    alert(`Successfully distributed all left income! (${successful} ranks processed)`);
                } else {
                    alert(`Distribution completed with some errors. Successful: ${successful}, Failed: ${failed}`);
                }

                // Refresh the data
                loadUserLeftIncome();
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred during bulk distribution');
            });
    }
</script>

</html>