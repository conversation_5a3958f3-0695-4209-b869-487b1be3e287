<?php
include('./lib/admin_auth.php');
include('./lib/db_connection.php');
date_default_timezone_set("Asia/Kolkata");

// Set page variables for menu highlighting
$page = 10;
$sub_page = 105;

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    switch ($_POST['action']) {
        case 'get_user_left_income':
            $user_id = intval($_POST['user_id']);
            
            // Get user details
            $user_query = dbQuery("
                SELECT u.id, u.name, u.email, u.current_rank_id, r.name as current_rank_name,
                       u.date_added as registration_date
                FROM tabl_user u
                LEFT JOIN tabl_ranks r ON u.current_rank_id = r.id
                WHERE u.id = '$user_id'
            ");
            
            if (dbNumRows($user_query) == 0) {
                echo json_encode(['status' => 'error', 'message' => 'User not found']);
                exit;
            }
            
            $user = dbFetchAssoc($user_query);
            $current_rank_id = $user['current_rank_id'];
            
            if ($current_rank_id <= 1) {
                echo json_encode(['status' => 'error', 'message' => 'User has no previous ranks to calculate left income']);
                exit;
            }
            
            // Calculate left income for each previous rank
            $left_income_data = [];
            
            // Get all ranks below current rank
            $previous_ranks_query = dbQuery("
                SELECT id, name, daily_credit 
                FROM tabl_ranks 
                WHERE id < '$current_rank_id' 
                ORDER BY id ASC
            ");
            
            while ($rank = dbFetchAssoc($previous_ranks_query)) {
                $rank_id = $rank['id'];
                $rank_name = $rank['name'];
                $daily_credit = $rank['daily_credit'];
                
                // Count days user received income from this rank (old cumulative system)
                $received_days_query = dbQuery("
                    SELECT COUNT(DISTINCT DATE(date)) as received_days
                    FROM tabl_walletsummery
                    WHERE user_id = '$user_id'
                    AND actiontype = 'daily_rank_income'
                    AND amount >= '$daily_credit'
                ");

                $received_days_result = dbFetchAssoc($received_days_query);
                $received_days = $received_days_result['received_days'] ?: 0;

                // Count days user received left cumulative income for this rank
                $left_income_received_query = dbQuery("
                    SELECT COUNT(*) as left_received_days
                    FROM tabl_walletsummery
                    WHERE user_id = '$user_id'
                    AND actiontype = 'left_cumulative_income'
                    AND order_id LIKE '%RANK_{$rank_id}_%'
                ");

                $left_received_result = dbFetchAssoc($left_income_received_query);
                $left_received_days = $left_received_result['left_received_days'] ?: 0;

                // Calculate total possible days (from registration to current date)
                $total_possible_days_query = dbQuery("
                    SELECT DATEDIFF(CURDATE(), DATE(u.date_added)) as total_days
                    FROM tabl_user u
                    WHERE u.id = '$user_id'
                ");

                $total_days_result = dbFetchAssoc($total_possible_days_query);
                $total_possible_days = $total_days_result['total_days'] ?: 0;

                // Adjust received days to exclude already distributed left income
                $actual_received_days = $received_days - $left_received_days;
                
                // Calculate left days and amount
                $left_days = max(0, $total_possible_days - $actual_received_days);
                $left_amount = $left_days * $daily_credit;

                $left_income_data[] = [
                    'rank_id' => $rank_id,
                    'rank_name' => $rank_name,
                    'daily_credit' => $daily_credit,
                    'received_days' => $actual_received_days,
                    'left_received_days' => $left_received_days,
                    'total_possible_days' => $total_possible_days,
                    'left_days' => $left_days,
                    'left_amount' => $left_amount
                ];
            }
            
            echo json_encode([
                'status' => 'success',
                'user' => $user,
                'left_income_data' => $left_income_data
            ]);
            exit;
            
        case 'distribute_left_income':
            $user_id = intval($_POST['user_id']);
            $rank_id = intval($_POST['rank_id']);
            $amount = floatval($_POST['amount']);
            $days = intval($_POST['days']);
            
            if ($amount <= 0 || $days <= 0) {
                echo json_encode(['status' => 'error', 'message' => 'Invalid amount or days']);
                exit;
            }
            
            // Get user and rank details
            $user_query = dbQuery("SELECT name FROM tabl_user WHERE id = '$user_id'");
            $rank_query = dbQuery("SELECT name FROM tabl_ranks WHERE id = '$rank_id'");
            
            if (dbNumRows($user_query) == 0 || dbNumRows($rank_query) == 0) {
                echo json_encode(['status' => 'error', 'message' => 'User or rank not found']);
                exit;
            }
            
            $user = dbFetchAssoc($user_query);
            $rank = dbFetchAssoc($rank_query);
            
            try {
                // Start transaction
                dbQuery("START TRANSACTION");
                
                // Add to user wallet
                $wallet_query = dbQuery("SELECT * FROM tabl_wallet WHERE user_id = '$user_id' AND user_type = 1");
                if (dbNumRows($wallet_query) > 0) {
                    dbQuery("UPDATE tabl_wallet SET 
                        amount = amount + '$amount',
                        date = NOW() 
                        WHERE user_id = '$user_id' AND user_type = 1
                    ");
                } else {
                    dbQuery("INSERT INTO tabl_wallet SET 
                        user_id = '$user_id',
                        user_type = '1',
                        amount = '$amount',
                        status = '1',
                        date = NOW()
                    ");
                }
                
                // Log in wallet summary
                $order_id = 'LEFT_CUMULATIVE_RANK_' . $rank_id . '_' . date('Ymd_His') . '_' . $user_id;
                dbQuery("INSERT INTO tabl_walletsummery SET 
                    user_id = '$user_id',
                    user_type = '1',
                    order_id = '$order_id',
                    amount = '$amount',
                    type = 'credit',
                    actiontype = 'left_cumulative_income',
                    date = NOW()
                ");
                
                // Add notification
                dbQuery("INSERT INTO tabl_notification SET 
                    user_id = '$user_id',
                    type = 'left_income',
                    title = 'Left Cumulative Income Received',
                    description = 'You received ₹$amount as left cumulative income from {$rank['name']} rank for $days days.',
                    data = '$amount',
                    status = '0',
                    date = NOW()
                ");
                
                // Log admin action
                dbQuery("INSERT INTO tabl_admin_logs SET 
                    admin_id = '$admin_id',
                    action = 'distribute_left_income',
                    description = 'Distributed ₹$amount left cumulative income from {$rank['name']} rank to {$user['name']} (ID: $user_id) for $days days',
                    date = NOW()
                ");
                
                dbQuery("COMMIT");
                
                echo json_encode([
                    'status' => 'success',
                    'message' => "Successfully distributed ₹$amount to {$user['name']}"
                ]);
                
            } catch (Exception $e) {
                dbQuery("ROLLBACK");
                echo json_encode(['status' => 'error', 'message' => 'Failed to distribute income: ' . $e->getMessage()]);
            }
            exit;
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Left Cumulative Income Management</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .income-card {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            background: #f8f9fa;
        }
        .rank-card {
            border: 1px solid #007bff;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            background: #e3f2fd;
        }
        .left-amount {
            font-size: 1.2em;
            font-weight: bold;
            color: #28a745;
        }
        .received-amount {
            color: #6c757d;
        }
        .search-section {
            background: #ffffff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <?php include("./inc/__header.php"); ?>

    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h3><i class="fas fa-coins"></i> Left Cumulative Income Management</h3>
                        <p class="mb-0">Manage and distribute left behind cumulative income from previous ranks</p>
                    </div>
                    <div class="card-body">
                        
                        <!-- Search User Section -->
                        <div class="search-section">
                            <h5><i class="fas fa-search"></i> Search User</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <label for="user_id">User ID:</label>
                                    <input type="number" id="user_id" class="form-control" placeholder="Enter User ID">
                                </div>
                                <div class="col-md-6 d-flex align-items-end">
                                    <button onclick="searchUser()" class="btn btn-primary">
                                        <i class="fas fa-search"></i> Search User
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- User Details Section -->
                        <div id="user-details" style="display: none;">
                            <div class="income-card">
                                <h5><i class="fas fa-user"></i> User Details</h5>
                                <div id="user-info"></div>
                            </div>
                        </div>
                        
                        <!-- Left Income Details -->
                        <div id="left-income-details" style="display: none;">
                            <h5><i class="fas fa-chart-line"></i> Left Cumulative Income by Rank</h5>
                            <div id="rank-income-cards"></div>

                            <!-- Bulk Distribution Section -->
                            <div id="bulk-distribution" style="display: none; margin-top: 20px;">
                                <div class="card">
                                    <div class="card-header bg-warning">
                                        <h6><i class="fas fa-hand-holding-usd"></i> Bulk Distribution</h6>
                                    </div>
                                    <div class="card-body">
                                        <p>Distribute all available left cumulative income at once:</p>
                                        <button onclick="distributeAllLeftIncome()" class="btn btn-warning btn-lg">
                                            <i class="fas fa-coins"></i> Distribute All Left Income
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function searchUser() {
            const userId = document.getElementById('user_id').value;
            
            if (!userId) {
                alert('Please enter a User ID');
                return;
            }
            
            // Show loading
            document.getElementById('user-details').style.display = 'none';
            document.getElementById('left-income-details').style.display = 'none';
            
            fetch('left_cumulative_income.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=get_user_left_income&user_id=${userId}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    displayUserDetails(data.user);
                    displayLeftIncomeDetails(data.left_income_data, userId);
                } else {
                    alert(data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while searching for the user');
            });
        }
        
        function displayUserDetails(user) {
            const userInfo = `
                <div class="row">
                    <div class="col-md-3"><strong>Name:</strong> ${user.name}</div>
                    <div class="col-md-3"><strong>Email:</strong> ${user.email}</div>
                    <div class="col-md-3"><strong>Current Rank:</strong> ${user.current_rank_name || 'No Rank'}</div>
                    <div class="col-md-3"><strong>Registration:</strong> ${user.registration_date}</div>
                </div>
            `;
            
            document.getElementById('user-info').innerHTML = userInfo;
            document.getElementById('user-details').style.display = 'block';
        }
        
        function displayLeftIncomeDetails(leftIncomeData, userId) {
            let cardsHtml = '';
            let hasLeftIncome = false;

            if (leftIncomeData.length === 0) {
                cardsHtml = '<div class="alert alert-info">No previous ranks found or no left income to distribute.</div>';
            } else {
                leftIncomeData.forEach(rank => {
                    if (rank.left_amount > 0) {
                        hasLeftIncome = true;
                    }

                    cardsHtml += `
                        <div class="rank-card">
                            <div class="row">
                                <div class="col-md-8">
                                    <h6><i class="fas fa-trophy"></i> ${rank.rank_name} (Rank ${rank.rank_id})</h6>
                                    <p class="mb-1"><strong>Daily Credit:</strong> ₹${rank.daily_credit}</p>
                                    <p class="mb-1"><strong>Days Received (Old System):</strong> ${rank.received_days} / ${rank.total_possible_days} days</p>
                                    <p class="mb-1"><strong>Left Income Already Given:</strong> ${rank.left_received_days} days</p>
                                    <p class="mb-1"><strong>Remaining Left Days:</strong> ${rank.left_days} days</p>
                                    <p class="left-amount">Remaining Left Amount: ₹${rank.left_amount}</p>
                                </div>
                                <div class="col-md-4 d-flex align-items-center">
                                    ${rank.left_amount > 0 ? `
                                        <button onclick="distributeIncome(${userId}, ${rank.rank_id}, ${rank.left_amount}, ${rank.left_days}, '${rank.rank_name}')"
                                                class="btn btn-success btn-sm">
                                            <i class="fas fa-hand-holding-usd"></i> Distribute ₹${rank.left_amount}
                                        </button>
                                    ` : `
                                        <span class="text-muted">No left income</span>
                                    `}
                                </div>
                            </div>
                        </div>
                    `;
                });
            }

            document.getElementById('rank-income-cards').innerHTML = cardsHtml;
            document.getElementById('left-income-details').style.display = 'block';

            // Show bulk distribution if there's left income
            if (hasLeftIncome) {
                document.getElementById('bulk-distribution').style.display = 'block';
                // Store data for bulk distribution
                window.currentUserLeftIncomeData = {userId: userId, leftIncomeData: leftIncomeData};
            } else {
                document.getElementById('bulk-distribution').style.display = 'none';
            }
        }
        
        function distributeIncome(userId, rankId, amount, days, rankName) {
            if (!confirm(`Are you sure you want to distribute ₹${amount} from ${rankName} rank for ${days} days?`)) {
                return;
            }
            
            fetch('left_cumulative_income.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=distribute_left_income&user_id=${userId}&rank_id=${rankId}&amount=${amount}&days=${days}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    alert(data.message);
                    // Refresh the data
                    searchUser();
                } else {
                    alert(data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while distributing income');
            });
        }

        function distributeAllLeftIncome() {
            if (!window.currentUserLeftIncomeData) {
                alert('No user data available');
                return;
            }

            const {userId, leftIncomeData} = window.currentUserLeftIncomeData;
            const ranksWithLeftIncome = leftIncomeData.filter(rank => rank.left_amount > 0);

            if (ranksWithLeftIncome.length === 0) {
                alert('No left income to distribute');
                return;
            }

            const totalAmount = ranksWithLeftIncome.reduce((sum, rank) => sum + rank.left_amount, 0);
            const ranksList = ranksWithLeftIncome.map(rank => `${rank.rank_name}: ₹${rank.left_amount}`).join('\n');

            if (!confirm(`Are you sure you want to distribute all left income?\n\nTotal Amount: ₹${totalAmount}\n\nBreakdown:\n${ranksList}`)) {
                return;
            }

            // Distribute each rank's left income sequentially
            let distributionPromises = ranksWithLeftIncome.map(rank => {
                return fetch('left_cumulative_income.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `action=distribute_left_income&user_id=${userId}&rank_id=${rank.rank_id}&amount=${rank.left_amount}&days=${rank.left_days}`
                }).then(response => response.json());
            });

            Promise.all(distributionPromises)
                .then(results => {
                    const successful = results.filter(result => result.status === 'success').length;
                    const failed = results.filter(result => result.status === 'error').length;

                    if (failed === 0) {
                        alert(`Successfully distributed all left income! (${successful} ranks processed)`);
                    } else {
                        alert(`Distribution completed with some errors. Successful: ${successful}, Failed: ${failed}`);
                    }

                    // Refresh the data
                    searchUser();
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred during bulk distribution');
                });
        }
    </script>
</body>
</html>
