<?php
/**
 * Test Script for Daily Income Logic Verification
 * 
 * This script helps you test the daily income distribution logic without actually running it.
 * It shows you what would happen if the cron job ran today.
 */

include('admin/lib/db_connection.php');
date_default_timezone_set("Asia/Kolkata");

/**
 * Check if user has active subscription
 */
function hasActiveSubscription($user_id) {
    $query = dbQuery("
        SELECT COUNT(*) as active_count 
        FROM tabl_user_subscriptions 
        WHERE user_id = '$user_id' 
        AND status = '1'
        AND (end_date IS NULL OR end_date >= CURDATE())
    ");
    
    $result = dbFetchAssoc($query);
    return $result['active_count'] > 0;
}

/**
 * Calculate cumulative daily income for user's rank
 */
function calculateCumulativeDailyIncome($current_rank_id) {
    if ($current_rank_id <= 0) {
        return ['total_income' => 0, 'breakdown' => []];
    }
    
    $ranks_query = dbQuery("
        SELECT id, name, daily_credit 
        FROM tabl_ranks 
        WHERE id <= '$current_rank_id' 
        ORDER BY id ASC
    ");
    
    $total_income = 0;
    $rank_breakdown = [];
    
    while ($rank = dbFetchAssoc($ranks_query)) {
        $daily_credit = floatval($rank['daily_credit']);
        $total_income += $daily_credit;
        $rank_breakdown[] = [
            'name' => $rank['name'],
            'credit' => $daily_credit
        ];
    }
    
    return [
        'total_income' => $total_income,
        'breakdown' => $rank_breakdown
    ];
}

/**
 * Check if daily income already distributed today for user
 */
function isDailyIncomeAlreadyDistributed($user_id, $date) {
    $query = dbQuery("
        SELECT COUNT(*) as count 
        FROM tabl_walletsummery 
        WHERE user_id = '$user_id' 
        AND actiontype = 'daily_rank_income' 
        AND DATE(date) = '$date'
    ");
    
    $result = dbFetchAssoc($query);
    return $result['count'] > 0;
}

$today = date('Y-m-d');

echo "<h1>Daily Income Distribution Logic Test - $today</h1>";
echo "<p>This shows what would happen if the daily income cron job ran today.</p>";

// Get all ranks for reference
echo "<h2>Rank Income Structure</h2>";
$ranks_query = dbQuery("SELECT * FROM tabl_ranks ORDER BY id ASC");
echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; margin-bottom: 20px;'>";
echo "<tr style='background: #f0f0f0;'>
        <th>Rank ID</th>
        <th>Name</th>
        <th>Daily Credit</th>
        <th>Cumulative Income</th>
        <th>Calculation</th>
      </tr>";

$cumulative = 0;
$calculation_parts = [];
while ($rank = dbFetchAssoc($ranks_query)) {
    $cumulative += $rank['daily_credit'];
    $calculation_parts[] = "₹{$rank['daily_credit']}";
    $calculation = implode(' + ', $calculation_parts) . " = ₹{$cumulative}";
    
    echo "<tr>
            <td>{$rank['id']}</td>
            <td><strong>{$rank['name']}</strong></td>
            <td>₹{$rank['daily_credit']}</td>
            <td><strong>₹{$cumulative}</strong></td>
            <td><small>{$calculation}</small></td>
          </tr>";
}
echo "</table>";

// Get all users with ranks
$users_query = dbQuery("
    SELECT u.id, u.name, u.email, u.current_rank_id, r.name as rank_name, r.daily_credit
    FROM tabl_user u
    LEFT JOIN tabl_ranks r ON u.current_rank_id = r.id
    WHERE u.id > 1 
    AND u.current_rank_id > 0
    ORDER BY u.current_rank_id DESC, u.id ASC
");

echo "<h2>Users Income Analysis</h2>";
echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%;'>";
echo "<tr style='background: #f0f0f0;'>
        <th>User ID</th>
        <th>Name</th>
        <th>Current Rank</th>
        <th>Active Sub</th>
        <th>Already Paid Today</th>
        <th>Income Breakdown</th>
        <th>Total Income</th>
        <th>Action</th>
      </tr>";

$total_checked = 0;
$would_receive = 0;
$no_subscription = 0;
$already_paid = 0;
$total_amount_would_distribute = 0;

while ($user = dbFetchAssoc($users_query)) {
    $user_id = $user['id'];
    $user_name = $user['name'];
    $current_rank_id = $user['current_rank_id'];
    $rank_name = $user['rank_name'];
    
    $total_checked++;
    
    // Check subscription
    $has_subscription = hasActiveSubscription($user_id);
    $subscription_status = $has_subscription ? '✅ Yes' : '❌ No';
    
    // Check if already paid today
    $already_paid_today = isDailyIncomeAlreadyDistributed($user_id, $today);
    $paid_status = $already_paid_today ? '✅ Yes' : '❌ No';
    
    // Calculate income
    $income_data = calculateCumulativeDailyIncome($current_rank_id);
    $total_income = $income_data['total_income'];
    
    // Create breakdown display
    $breakdown_display = '';
    foreach ($income_data['breakdown'] as $rank_info) {
        $breakdown_display .= "{$rank_info['name']}: ₹{$rank_info['credit']}<br>";
    }
    
    // Determine action
    $action = 'Skip';
    $action_color = '#ffcccc';
    
    if (!$has_subscription) {
        $action = 'Skip - No subscription';
        $no_subscription++;
    } elseif ($already_paid_today) {
        $action = 'Skip - Already paid today';
        $action_color = '#ffffcc';
        $already_paid++;
    } elseif ($total_income > 0) {
        $action = '💰 WOULD RECEIVE ₹' . $total_income;
        $action_color = '#ccffcc';
        $would_receive++;
        $total_amount_would_distribute += $total_income;
    }
    
    echo "<tr style='background: $action_color;'>
            <td>$user_id</td>
            <td>$user_name</td>
            <td>$rank_name (ID: $current_rank_id)</td>
            <td>$subscription_status</td>
            <td>$paid_status</td>
            <td><small>$breakdown_display</small></td>
            <td><strong>₹$total_income</strong></td>
            <td><strong>$action</strong></td>
          </tr>";
}

echo "</table>";

echo "<h2>Summary</h2>";
echo "<ul>";
echo "<li><strong>Total users checked:</strong> $total_checked</li>";
echo "<li><strong>Would receive income:</strong> $would_receive</li>";
echo "<li><strong>Skipped - No subscription:</strong> $no_subscription</li>";
echo "<li><strong>Skipped - Already paid today:</strong> $already_paid</li>";
echo "<li><strong>Total amount would distribute:</strong> ₹$total_amount_would_distribute</li>";
echo "</ul>";

// Show income distribution summary
echo "<h2>Income Distribution Summary</h2>";
if ($would_receive > 0) {
    echo "<p style='color: green;'><strong>$would_receive users would receive daily income totaling ₹$total_amount_would_distribute!</strong></p>";
} else {
    echo "<p style='color: blue;'>No users would receive income today (all already paid or no active subscriptions).</p>";
}

// Show example calculations
echo "<h2>Example Income Calculations</h2>";
echo "<div style='background: #f9f9f9; padding: 15px; border: 1px solid #ddd; margin: 10px 0;'>";
echo "<h4>How Cumulative Income Works:</h4>";
echo "<ul>";
echo "<li><strong>Rank 1 User:</strong> Gets ₹50 (only rank 1 income)</li>";
echo "<li><strong>Rank 2 User:</strong> Gets ₹50 + ₹100 = ₹150 (rank 1 + rank 2 income)</li>";
echo "<li><strong>Rank 3 User:</strong> Gets ₹50 + ₹100 + ₹200 = ₹350 (rank 1 + rank 2 + rank 3 income)</li>";
echo "</ul>";
echo "<p><em>This encourages users to achieve higher ranks for better daily income.</em></p>";
echo "</div>";

// Show cron commands
echo "<h2>Cron Job Commands</h2>";
echo "<p>To set up the daily income distribution cron job:</p>";
echo "<pre style='background: #f0f0f0; padding: 10px; border: 1px solid #ccc;'>";
echo "# Run daily at 6 AM\n";
echo "0 6 * * * /usr/bin/php " . realpath('cron_daily_income.php') . "\n\n";
echo "# Run daily at 8 AM\n";
echo "0 8 * * * /usr/bin/php " . realpath('cron_daily_income.php') . "\n\n";
echo "# Run every 10 minutes (for testing only)\n";
echo "*/10 * * * * /usr/bin/php " . realpath('cron_daily_income.php') . "\n";
echo "</pre>";

echo "<h2>Manual Testing</h2>";
echo "<p>To test the daily income distribution manually:</p>";
echo "<pre style='background: #f0f0f0; padding: 10px; border: 1px solid #ccc;'>";
echo "php " . realpath('cron_daily_income.php') . "\n";
echo "</pre>";

echo "<p><a href='setup_cron_daily_income.php'>Go to Setup & Test Interface</a></p>";
?>
