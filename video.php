<?php
session_start();
include('admin/lib/db_connection.php');
include('lib/translation_helper.php');
date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d H:i:s');

// Get current language ID for filtering posts
$current_language_id = getCurrentLanguageId();

?>


<!doctype html>
<html class="no-js" lang="">



<head>
    <base href="<?= BASE_URL; ?>">
    <meta charset="utf-8">
    <meta http-equiv="x-ua-compatible" content="ie=edge">
    <title>Win TV 99</title>
    <meta name="description"
        content="Win TV 99 is your go-to destination for the latest news, updates, and insightful stories from across India and around the globe. We are committed to delivering accurate, reliable, and unbiased journalism that empowers our readers with knowledge and understanding">
    <meta name="keywords" content="Spiritual,travel,story,Innovation,stay,news">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!-- Favicon -->
    <link rel="shortcut icon" type="image/x-icon" href="img/favicon2.png">
    <!-- Normalize CSS -->
    <link rel="stylesheet" href="css/normalize.css">
    <!-- Main CSS -->
    <link rel="stylesheet" href="css/main.css">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" rel="stylesheet"
        integrity="sha384-EVSTQN3/azprG1Anm3QDgpJLIm9Nao0Yz1ztcQTwFspd3yD65VohhpuuCOmLASjC" crossorigin="anonymous">
    <link rel="stylesheet" href="css/bootstrap.min.css">

    <!-- Animate CSS -->
    <link rel="stylesheet" href="css/animate.min.css">
    <!-- Font-awesome CSS-->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="css/font-awesome.min.css">
    <!-- Owl Caousel CSS -->
    <link rel="stylesheet" href="vendor/OwlCarousel/owl.carousel.min.css">
    <link rel="stylesheet" href="vendor/OwlCarousel/owl.theme.default.min.css">
    <!-- Main Menu CSS -->
    <link rel="stylesheet" href="css/meanmenu.min.css">
    <!-- Nivo Slider CSS-->
    <link rel="stylesheet" href="vendor/slider/css/nivo-slider.css" type="text/css" />
    <link rel="stylesheet" href="vendor/slider/css/preview.css" type="text/css" media="screen" />
    <!-- Magnific CSS -->
    <link rel="stylesheet" type="text/css" href="css/magnific-popup.css">
    <!-- Switch Style CSS -->
    <link rel="stylesheet" href="css/hover-min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="style.css">

    <!-- For IE -->
    <link rel="stylesheet" type="text/css" href="css/ie-only.css" />
    <link rel="stylesheet" href="css/me.css">
    <link rel="stylesheet" href="css/custom.css">
    <link rel="stylesheet" href="css/lightfooter.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <!-- Modernizr Js -->
    <script src="js/modernizr-2.8.3.min.js"></script>
    <style>
        section.bg-accent-dark:nth-child(odd) {
            background: #f6f6f6;
        }

        .bg-accent-dark {
            padding: 20px 0;
        }
    </style>
</head>

<body>
    <div id="main">

        <div id="wrapper">
            <?php include("./inc/header_mobile.php"); ?>

            <?php include("./inc/header.php"); ?>

            <div class="d-lg-none d-block" style="padding-top: 50px;"> </div>
            <div class="d-none d-lg-block" style="padding-top: 130px;"> </div>



            <!-- Header Area End Here -->


            <!-- Feature News Area Start Here -->
            <?php
            // Get featured video post for main video player
            $featured_video_query = dbQuery("
                SELECT * FROM tabl_post
                WHERE (video != '' OR video_link != '')
                AND status = '1'
                AND language = '$current_language_id'
                ORDER BY id DESC
                LIMIT 1
            ");
            $featured_video = dbFetchAssoc($featured_video_query);

            // Get related videos for sidebar
            $related_videos_query = dbQuery("
                SELECT * FROM tabl_post
                WHERE (video != '' OR video_link != '')
                AND status = '1'
                AND language = '$current_language_id'
                ORDER BY id DESC
                LIMIT 1, 1
            ");
            ?>

            <section class="section-space-less30" id="video">
                <div class="container">
                    <div class="row tab-space5">
                        <h1 class="cat-heading text-left"><?= t('videos', 'Videos'); ?></h1>
                        <div class="col-lg-8 col-md-12 mb-10">
                            <div class="row no-gutters mt-5">
                                <?php if ($featured_video): ?>
                                    <?php if (!empty($featured_video['video'])): ?>
                                        <!-- Local video file -->
                                        <video class="video width-100" width="180" controls="">
                                            <source src="./assets/video/<?= $featured_video['video']; ?>" type="video/mp4">
                                            <?= t('video_not_supported', 'Your browser does not support the video tag.'); ?>
                                        </video>
                                    <?php elseif (!empty($featured_video['video_link'])): ?>
                                        <!-- External video link (YouTube, etc.) -->
                                        <div class="video-container width-100">
                                            <?php
                                            $video_link = $featured_video['video_link'];
                                            // Convert YouTube watch URLs to embed URLs
                                            if (strpos($video_link, 'youtube.com/watch') !== false) {
                                                $video_link = str_replace('watch?v=', 'embed/', $video_link);
                                            } elseif (strpos($video_link, 'youtu.be/') !== false) {
                                                $video_link = str_replace('youtu.be/', 'youtube.com/embed/', $video_link);
                                            }
                                            ?>
                                            <iframe width="100%" height="315" src="<?= $video_link; ?>" frameborder="0" allowfullscreen></iframe>
                                        </div>
                                    <?php endif; ?>

                                    <!-- Video title and description -->
                                    <div class="video-info mt-3">
                                        <h3><?= $featured_video['headline'] . ": " . $featured_video['title']; ?></h3>
                                        <?php if (!empty($featured_video['description'])): ?>
                                            <p class="video-description"><?= substr($featured_video['description'], 0, 200) . '...'; ?></p>
                                        <?php endif; ?>
                                    </div>
                                <?php else: ?>
                                    <!-- Fallback if no videos found -->
                                    <div class="no-video-message text-center p-5">
                                        <h3><?= t('no_videos_available', 'No videos available in your language'); ?></h3>
                                        <p><?= t('check_back_later', 'Please check back later for new content'); ?></p>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <div class="col-lg-4 col-md-12">
                            <div class="row mt-5 tab-space5">
                                <?php if (dbFetchAssoc($related_videos_query)): ?>
                                    <?php
                                    // Reset the query to get the result again
                                    $related_videos_query = dbQuery("
                                        SELECT * FROM tabl_post
                                        WHERE (video != '' OR video_link != '')
                                        AND status = '1'
                                        AND language = '$current_language_id'
                                        ORDER BY id DESC
                                        LIMIT 1, 1
                                    ");
                                    $related_video = dbFetchAssoc($related_videos_query);

                                    // Get category details for the related video
                                    $cat_details = dbQuery("SELECT tabl_post_to_category.*,tabl_category.category,tabl_category.slug as catslug FROM tabl_post_to_category INNER JOIN tabl_category ON tabl_post_to_category.category_id=tabl_category.id WHERE tabl_post_to_category.post_id='" . $related_video['id'] . "'");
                                    $res_cat_details = dbFetchAssoc($cat_details);
                                    ?>
                                    <div class="col-12">
                                        <a href="<?php echo BASE_URL . '' . $res_cat_details['catslug'] . '/' . $related_video['slug']; ?>.html" title="<?php echo $related_video['headline'] . " : " . $related_video['title']; ?>" class="next-video">
                                            <span class="thumb">
                                                <figure>
                                                    <img src="./assets/post/<?php echo $related_video['image']; ?>" alt="<?php echo $related_video['headline'] . " : " . $related_video['title']; ?>" class="lazy" width="298" height="168">
                                                </figure>
                                                <span class="icon video-paly"></span>
                                            </span>
                                            <p class="caption"><?php echo $related_video['headline'] . " : " . $related_video['title']; ?></p>
                                        </a>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section>
                <div class="container">
                    <div class="row tab-space5">
                        <?php
                        // Get more videos for the grid section
                        $more_videos_query = dbQuery("
                            SELECT * FROM tabl_post
                            WHERE (video != '' OR video_link != '')
                            AND status = '1'
                            AND language = '$current_language_id'
                            ORDER BY id DESC
                            LIMIT 2, 4
                        ");

                        $video_count = 0;
                        while (($video_post = dbFetchAssoc($more_videos_query)) && $video_count < 4) {
                            // Get category details
                            $cat_details = dbQuery("SELECT tabl_post_to_category.*,tabl_category.category,tabl_category.slug as catslug FROM tabl_post_to_category INNER JOIN tabl_category ON tabl_post_to_category.category_id=tabl_category.id WHERE tabl_post_to_category.post_id='" . $video_post['id'] . "'");
                            $res_cat_details = dbFetchAssoc($cat_details);
                            $video_count++;
                        ?>
                            <div class="col-lg-3 col-md-12">
                                <div class="row mt-2 tab-space5">
                                    <div class="col-12">
                                        <a href="<?php echo BASE_URL . '' . $res_cat_details['catslug'] . '/' . $video_post['slug']; ?>.html" title="<?php echo $video_post['headline'] . " : " . $video_post['title']; ?>" class="next-video">
                                            <span class="thumb">
                                                <figure>
                                                    <img src="./assets/post/<?php echo $video_post['image']; ?>" alt="<?php echo $video_post['headline'] . " : " . $video_post['title']; ?>" class="lazy" width="298" height="168">
                                                </figure>
                                                <span class="icon video-paly"></span>
                                            </span>
                                            <p class="caption"><?php echo $video_post['headline'] . " : " . $video_post['title']; ?></p>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        <?php
                        }

                        // Fill remaining slots if we have fewer than 4 videos
                        while ($video_count < 4) {
                            $video_count++;
                        ?>
                            <div class="col-lg-3 col-md-12">
                                <div class="row mt-2 tab-space5">
                                    <div class="col-12">
                                        <div class="no-video-placeholder text-center p-3">
                                            <p><?= t('more_videos_coming_soon', 'More videos coming soon'); ?></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php } ?>
                    </div>
                </div>
            </section>

            <section class="bg-accent-dark">

                <div class="container">
                    <div class="row">
                        <div class="col-lg-12">
                            <h2 class="cat-heading d-lg-block text-left">
                                <?= t('latest_videos', 'Latest Videos'); ?>
                            </h2>

                            <div class="row mt-5">
                                <?php
                                $sel_news = dbQuery("SELECT * FROM tabl_post WHERE (video!='' OR video_link!='') AND status='1' AND language='$current_language_id' ORDER BY id DESC LIMIT 4");
                                ?>

                                <div class="col-md-12 col-sm-12">
                                    <div class="row">


                                        <div class="latest-carousel owl-carousel owl-theme">

                                            <?php
                                            // $sel_news = dbQuery("SELECT * FROM tabl_post WHERE video!='' OR video_link!='' ORDER BY headline ASC LIMIT 0,10");
                                            while ($res_news = dbFetchAssoc($sel_news)) {

                                                $cat_details = dbQuery("SELECT tabl_post_to_category.*,tabl_category.category,tabl_category.slug as catslug FROM tabl_post_to_category INNER JOIN tabl_category ON tabl_post_to_category.category_id=tabl_category.id WHERE tabl_post_to_category.post_id='" . $res_news['id'] . "'");
                                                $res_cat_details = dbFetchAssoc($cat_details);
                                            ?>

                                                <a href="<?php echo BASE_URL . '' . $res_cat_details['catslug'] . '/' . $res_news['slug']; ?>.html" title="<?php echo $res_news['headline'] . " : " . $res_news['title']; ?>" class="next-video">
                                                    <span class="thumb">
                                                        <figure>
                                                            <img src="./assets/post/<?php echo $res_news['image']; ?>" alt="<?php echo $res_news['headline'] . " : " . $res_news['title']; ?>" class="lazy" width="298" height="168" style="">
                                                        </figure>
                                                        <span class="icon video-paly"></span>
                                                    </span>
                                                    <p class="caption"><?php echo $res_news['headline'] . " : " . $res_news['title']; ?></p>
                                                </a>
                                            <?php
                                            }
                                            ?>
                                        </div>

                                        <div class="view text-center mt-5">
                                            <a href="<?= BASE_URL ?>video" class="viewall"><?= t('view_all', 'View All'); ?> <i class="fa fa-long-arrow-right" aria-hidden="true"></i></a>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </section>

            <?php
            // Get popular categories for dynamic sections
            $popular_categories = dbQuery("SELECT * FROM tabl_category WHERE is_popular='1' AND status='1' ORDER BY order_no ASC, id ASC");

            while ($category = dbFetchAssoc($popular_categories)) {
                // Get videos for this category
                $category_videos = dbQuery("
                    SELECT p.* FROM tabl_post p
                    INNER JOIN tabl_post_to_category ptc ON p.id = ptc.post_id
                    WHERE ptc.category_id = '" . $category['id'] . "'
                    AND (p.video != '' OR p.video_link != '')
                    AND p.status = '1'
                    AND p.language = '$current_language_id'
                    ORDER BY p.id DESC
                    LIMIT 4
                ");

                // Only show section if there are videos
                if (dbNumRows($category_videos) > 0) {
            ?>
                    <section class="bg-accent-dark">
                        <div class="container">
                            <div class="row">
                                <div class="col-lg-12">
                                    <h2 class="cat-heading d-lg-block text-left">
                                        <?= translateCategory($category['slug'], $category['category']); ?>
                                    </h2>

                                    <div class="row mt-5">
                                        <div class="col-md-12 col-sm-12">
                                            <div class="row">
                                                <div class="latest-carousel owl-carousel owl-theme">
                                                    <?php
                                                    while ($res_news = dbFetchAssoc($category_videos)) {
                                                        $cat_details = dbQuery("SELECT tabl_post_to_category.*,tabl_category.category,tabl_category.slug as catslug FROM tabl_post_to_category INNER JOIN tabl_category ON tabl_post_to_category.category_id=tabl_category.id WHERE tabl_post_to_category.post_id='" . $res_news['id'] . "'");
                                                        $res_cat_details = dbFetchAssoc($cat_details);
                                                    ?>
                                                        <a href="<?php echo BASE_URL . '' . $res_cat_details['catslug'] . '/' . $res_news['slug']; ?>.html" title="<?php echo $res_news['headline'] . " : " . $res_news['title']; ?>" class="next-video">
                                                            <span class="thumb">
                                                                <figure>
                                                                    <img src="./assets/post/<?php echo $res_news['image']; ?>" alt="<?php echo $res_news['headline'] . " : " . $res_news['title']; ?>" class="lazy" width="298" height="168" style="">
                                                                </figure>
                                                                <span class="icon video-paly"></span>
                                                            </span>
                                                            <p class="caption"><?php echo $res_news['headline'] . " : " . $res_news['title']; ?></p>
                                                        </a>
                                                    <?php
                                                    }
                                                    ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="view text-center mt-5">
                                        <a href="<?= BASE_URL ?>category/<?= $category['slug'] ?>" class="viewall"><?= t('view_all', 'View All'); ?> <i class="fa fa-long-arrow-right" aria-hidden="true"></i></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>
            <?php
                }
            }
            ?>









            <?php include("./inc/footer_main.php"); ?>
        </div>


        <!-- jquery-->
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/js/bootstrap.bundle.min.js"
            integrity="sha384-MrcW6ZMFYlzcLA8Nl+NtUVF0sA7MsXsP1UyJoMp4YLEuNSfAP+JcXn/tWtIaxVXM"
            crossorigin="anonymous"></script>
        <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.2/dist/umd/popper.min.js"
            integrity="sha384-IQsoLXl5PILFhosVNubq5LC7Qb9DXgDA9i+tQ8Zj3iwWAwPtgFTxbJ8NT4GN1R8p"
            crossorigin="anonymous"></script>
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/js/bootstrap.min.js"
            integrity="sha384-cVKIPhGWiC2Al4u+LWgxfKTRIcfu0JTxR+EQDz/bgldoEyl4H0zUF0QKbrJ0EcQF"
            crossorigin="anonymous"></script>
        <script src="js/jquery-2.2.4.min.js" type="text/javascript"></script>
        <!-- Plugins js -->
        <script src="js/plugins.js" type="text/javascript"></script>
        <!-- Popper js -->
        <script src="js/popper.js" type="text/javascript"></script>
        <!-- Bootstrap js -->
        <script src="js/bootstrap.min.js" type="text/javascript"></script>
        <!-- WOW JS -->
        <script src="js/wow.min.js"></script>
        <!-- Owl Cauosel JS -->
        <script src="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/owl.carousel.min.js" integrity="sha512-bPs7Ae6pVvhOSiIcyUClR7/q2OAsRiovw4vAkX+zJbw3ShAeeqezq50RIIcIURq7Oa20rW2n2q+fyXBNcU9lrw==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>

        <script>
            $('.latest-carousel').owlCarousel({
                loop: false,
                margin: 10,
                nav: true,
                navText: [
                    '<span class="custom-prev"><i class="fa fa-chevron-left"></i></span>',
                    '<span class="custom-next"><i class="fa fa-chevron-right"></i></span>'
                ],
                dots: false,
                responsive: {
                    0: {
                        items: 1
                    },
                    480: {
                        items: 2
                    },
                    600: {
                        items: 3
                    },
                    1000: {
                        items: 4
                    }
                }
            })
        </script>
        <!-- Meanmenu Js -->
        <script src="js/jquery.meanmenu.min.js" type="text/javascript"></script>
        <!-- Srollup js -->
        <script src="js/jquery.scrollUp.min.js" type="text/javascript"></script>
        <!-- jquery.counterup js -->
        <script src="js/jquery.counterup.min.js"></script>
        <script src="js/waypoints.min.js"></script>
        <!-- Nivo slider js -->
        <script src="vendor/slider/js/jquery.nivo.slider.js" type="text/javascript"></script>
        <script src="vendor/slider/home.js" type="text/javascript"></script>
        <!-- Isotope js -->
        <script src="js/isotope.pkgd.min.js" type="text/javascript"></script>
        <!-- Magnific Popup -->
        <script src="js/jquery.magnific-popup.min.js"></script>
        <!-- Ticker Js -->
        <script src="js/ticker.js" type="text/javascript"></script>
        <!-- Custom Js -->
        <script src="js/main.js" type="text/javascript"></script>

        <script>
            document.addEventListener('DOMContentLoaded', function() {
                const accordionItems = document.querySelectorAll('.accordion-item');

                accordionItems.forEach(function(item) {
                    const title = item.querySelector('.accordion-title');

                    title.addEventListener('click', function() {
                        const content = this.nextElementSibling;

                        if (content.style.display === 'block') {
                            content.style.display = 'none';
                            this.classList.remove('active');
                        } else {
                            content.style.display = 'block';
                            this.classList.add('active');
                        }
                    });
                });
            });
            // here another js
            function toggleAdditionalLinks(event) {
                var additionalLinks = document.getElementById("additionalLinks");
                var additionalLinkstour = document.getElementById("additionalLinks-tour");
                var additionalLinksstay = document.getElementById("additionalLinks-stay");

                // Prevent the click event from propagating to the document
                event.stopPropagation();

                // Hide other sections
                if (additionalLinkstour.style.display === "block") {
                    additionalLinkstour.style.display = "none";
                    document.removeEventListener('click', hideAdditionalLinkstourOnClickOutside);
                }

                if (additionalLinksstay.style.display === "block") {
                    additionalLinksstay.style.display = "none";
                    document.removeEventListener('click', hideAdditionalLinksstayOnClickOutside);
                }

                if (additionalLinks.style.display === "none") {
                    additionalLinks.style.display = "block";
                    document.addEventListener('click', hideAdditionalLinksOnClickOutside);
                } else {
                    additionalLinks.style.display = "none";
                    document.removeEventListener('click', hideAdditionalLinksOnClickOutside);
                }
            }

            function hideAdditionalLinksOnClickOutside(event) {
                var additionalLinks = document.getElementById("additionalLinks");
                if (!additionalLinks.contains(event.target) && event.target.id !== 'toggleLink') {
                    additionalLinks.style.display = "none";
                    document.removeEventListener('click', hideAdditionalLinksOnClickOutside);
                }
            }

            function toggleAdditionalLinkstour(event) {
                var additionalLinkstour = document.getElementById("additionalLinks-tour");
                var additionalLinks = document.getElementById("additionalLinks");
                var additionalLinksstay = document.getElementById("additionalLinks-stay");

                // Prevent the click event from propagating to the document
                event.stopPropagation();

                // Hide other sections
                if (additionalLinks.style.display === "block") {
                    additionalLinks.style.display = "none";
                    document.removeEventListener('click', hideAdditionalLinksOnClickOutside);
                }

                if (additionalLinksstay.style.display === "block") {
                    additionalLinksstay.style.display = "none";
                    document.removeEventListener('click', hideAdditionalLinksstayOnClickOutside);
                }

                if (additionalLinkstour.style.display === "none") {
                    additionalLinkstour.style.display = "block";
                    document.addEventListener('click', hideAdditionalLinkstourOnClickOutside);
                } else {
                    additionalLinkstour.style.display = "none";
                    document.removeEventListener('click', hideAdditionalLinkstourOnClickOutside);
                }
            }

            function hideAdditionalLinkstourOnClickOutside(event) {
                var additionalLinkstour = document.getElementById("additionalLinks-tour");
                if (!additionalLinkstour.contains(event.target) && event.target.id !== 'toggleLinkTour') {
                    additionalLinkstour.style.display = "none";
                    document.removeEventListener('click', hideAdditionalLinkstourOnClickOutside);
                }
            }

            function toggleAdditionalLinksstay(event) {
                var additionalLinksstay = document.getElementById("additionalLinks-stay");
                var additionalLinks = document.getElementById("additionalLinks");
                var additionalLinkstour = document.getElementById("additionalLinks-tour");

                // Prevent the click event from propagating to the document
                event.stopPropagation();

                // Hide other sections
                if (additionalLinks.style.display === "block") {
                    additionalLinks.style.display = "none";
                    document.removeEventListener('click', hideAdditionalLinksOnClickOutside);
                }

                if (additionalLinkstour.style.display === "block") {
                    additionalLinkstour.style.display = "none";
                    document.removeEventListener('click', hideAdditionalLinkstourOnClickOutside);
                }

                if (additionalLinksstay.style.display === "none") {
                    additionalLinksstay.style.display = "block";
                    document.addEventListener('click', hideAdditionalLinksstayOnClickOutside);
                } else {
                    additionalLinksstay.style.display = "none";
                    document.removeEventListener('click', hideAdditionalLinksstayOnClickOutside);
                }
            }

            function hideAdditionalLinksstayOnClickOutside(event) {
                var additionalLinksstay = document.getElementById("additionalLinks-stay");
                if (!additionalLinksstay.contains(event.target) && event.target.id !== 'toggleLinkStay') {
                    additionalLinksstay.style.display = "none";
                    document.removeEventListener('click', hideAdditionalLinksstayOnClickOutside);
                }
            }

            document.getElementById("toggleLink").addEventListener("click", toggleAdditionalLinks);
            document.getElementById("toggleLinkTour").addEventListener("click", toggleAdditionalLinkstour);
            document.getElementById("toggleLinkStay").addEventListener("click", toggleAdditionalLinksstay);


            // Detect scroll event
            window.onscroll = function() {
                scrollFunction()
            };

            function scrollFunction() {
                var footer = document.querySelector('.mobile-footer');
                if (document.body.scrollTop > 20 || document.documentElement.scrollTop > 20) {
                    footer.classList.add('show'); // Add class to show footer
                } else {
                    footer.classList.remove('show'); // Remove class to hide footer
                }
            }
        </script>
        <script>
            function toggleSection(headerElement) {
                const content = headerElement.nextElementSibling;
                const icon = headerElement.querySelector('.icon i');

                if (content.style.display === "block") {
                    content.style.display = "none";
                    icon.classList.remove('fa-chevron-down');
                    icon.classList.add('fa-chevron-up');
                } else {
                    content.style.display = "block";
                    icon.classList.remove('fa-chevron-up');
                    icon.classList.add('fa-chevron-down');
                }
            }


            $(document).ready(function() {
                $('#carouselExampleControls').carousel({
                    interval: 2000 // 2 seconds
                });
            });

            $(document).ready(function() {
                $('#menuIcon').on('click', function() {
                    $(this).toggleClass('bi-list bi-x');
                });

                $('#offcanvasExample').on('hidden.bs.offcanvas', function() {
                    $('#menuIcon').removeClass('bi-x').addClass('bi-list');
                });
            });


            $(document).ready(function() {
                $('#smenuIcon').on('click', function() {
                    $(this).toggleClass('bi-list bi-x');
                });

                $('#offcanvasExample').on('hidden.bs.offcanvas', function() {
                    $('#smenuIcon').removeClass('bi-x').addClass('bi-list');
                });
            });
        </script>
</body>


</html>