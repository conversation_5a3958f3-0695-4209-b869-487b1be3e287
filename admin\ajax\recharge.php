<?php 
session_start();
include('../lib/db_connection.php');

// Check if admin is logged in
if (!isset($_SESSION['admin_id'])) {
    echo '0';
    exit;
}

// Get parameters from AJAX request
$table_name = $_REQUEST['tabl'];
$status = $_REQUEST['val']; // 1 = Accept, 2 = Reject
$row_id = $_REQUEST['row_id'];
$user_id = $_REQUEST['user_id'];
$amount = $_REQUEST['amount'];

// Validate inputs
if (empty($table_name) || empty($status) || empty($row_id) || empty($user_id)) {
    echo '0';
    exit;
}

// Only process if table is tabl_deposits
if ($table_name !== 'tabl_deposits') {
    echo '0';
    exit;
}

try {
    // Get deposit details
    $deposit_query = dbQuery("SELECT * FROM tabl_deposits WHERE id='" . $row_id . "' AND user_id='" . $user_id . "'");
    $deposit_data = dbFetchAssoc($deposit_query);
    
    if (!$deposit_data) {
        echo '0';
        exit;
    }
    
    // Check if deposit is still pending (status = 0)
    if ($deposit_data['status'] != '0') {
        echo '0';
        exit;
    }
    
    // Update deposit status
    $update_deposit = dbQuery("UPDATE tabl_deposits SET status='" . $status . "' WHERE id='" . $row_id . "'");
    
    if ($update_deposit) {
        // If status is 1 (Accept), credit the amount to user's wallet
        if ($status == '1') {
            // Use the amount from the deposit record or the provided amount
            $credit_amount = !empty($amount) ? $amount : $deposit_data['total_amount'];
            
            // Check if user wallet exists
            $wallet_check = dbQuery("SELECT * FROM tabl_wallet WHERE user_id='" . $user_id . "' AND user_type='user'");
            
            if (dbNumRows($wallet_check) > 0) {
                // Update existing wallet
                update_wallet($user_id, $credit_amount, 'credit', '1');
            } else {
                // Create new wallet entry
                dbQuery("INSERT INTO tabl_wallet (user_id, user_type, amount) VALUES ('" . $user_id . "', 'user', '" . $credit_amount . "')");
            }
            
            // Log the transaction
            $transaction_note = "Deposit approved - Ref: " . $deposit_data['ref_num'];
            dbQuery("INSERT INTO tabl_walletsummery (user_id, user_type, order_id, amount, type, actiontype, date) VALUES ('" . $user_id . "', 'user', '" . $row_id . "', '" . $credit_amount . "', 'credit', 'deposit', NOW())");
        }
        
        echo '1';
    } else {
        echo '0';
    }
    
} catch (Exception $e) {
    echo '0';
}

?>
