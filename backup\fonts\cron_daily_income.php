<?php
/**
 * Cron Job: Daily Income Distribution System
 * 
 * This script runs daily to distribute income to users based on their current rank.
 * It calculates cumulative daily income from rank 1 up to user's current rank.
 * 
 * Example: If user is rank 3, they get income from rank 1 + rank 2 + rank 3
 * 
 * Schedule: Run daily at a specific time
 * Cron: 0 6 * * * /usr/bin/php /path/to/cron_daily_income.php
 */

// Set timezone and error reporting
date_default_timezone_set("Asia/Kolkata");
error_reporting(E_ALL);
ini_set('display_errors', 1);

// include_once database connection
include_once('admin/lib/db_connection.php');

// Log file for cron job activities
$log_file = 'logs/cron_daily_income_' . date('Y-m') . '.log';

// Ensure logs directory exists
if (!file_exists('logs')) {
    mkdir('logs', 0755, true);
}

/**
 * Log function for cron activities
 */
function logActivity($message) {
    global $log_file;
    $timestamp = date('Y-m-d H:i:s');
    $log_entry = "[$timestamp] $message" . PHP_EOL;
    file_put_contents($log_file, $log_entry, FILE_APPEND | LOCK_EX);
    echo $log_entry; // Also output to console
}

/**
 * Check if user has active subscription
 */
function hasActiveSubscription($user_id) {
    $query = dbQuery("
        SELECT COUNT(*) as active_count 
        FROM tabl_user_subscriptions 
        WHERE user_id = '$user_id' 
        AND status = '1'
        AND (end_date IS NULL OR end_date >= CURDATE())
    ");
    

    $result = dbFetchAssoc($query);
    return $result['active_count'] > 0;
}

/**
 * Calculate daily income for user's current rank only
 */
function calculateCurrentRankDailyIncome($current_rank_id) {
    if ($current_rank_id <= 0) {
        return [
            'total_income' => 0,
            'breakdown' => []
        ]; // No rank, no income
    }

    // Get only the current rank details
    $rank_query = dbQuery("
        SELECT id, name, daily_credit
        FROM tabl_ranks
        WHERE id = '$current_rank_id'
    ");

    if (dbNumRows($rank_query) == 0) {
        return [
            'total_income' => 0,
            'breakdown' => []
        ];
    }

    $rank = dbFetchAssoc($rank_query);
    $daily_credit = floatval($rank['daily_credit']);

    return [
        'total_income' => $daily_credit,
        'breakdown' => ["{$rank['name']}: ₹{$daily_credit}"]
    ];
}

/**
 * Check if daily income already distributed today for user
 */
function isDailyIncomeAlreadyDistributed($user_id, $date) {
    $query = dbQuery("
        SELECT COUNT(*) as count 
        FROM tabl_walletsummery 
        WHERE user_id = '$user_id' 
        AND actiontype = 'daily_rank_income' 
        AND DATE(date) = '$date'
    ");
    
    $result = dbFetchAssoc($query);
    return $result['count'] > 0;
}

/**
 * Distribute daily income to user
 */
function distributeDailyIncome($user_id, $user_name, $current_rank_id, $rank_name, $income_data) {
    $total_income = $income_data['total_income'];
    $breakdown = implode(', ', $income_data['breakdown']);
    $today = date('Y-m-d');
    
    if ($total_income <= 0) {
        logActivity("SKIP: User $user_name (ID: $user_id) has no income for rank $current_rank_id");
        return false;
    }
    
    // Check if already distributed today
    if (isDailyIncomeAlreadyDistributed($user_id, $today)) {
        logActivity("SKIP: User $user_name (ID: $user_id) already received daily income today");
        return false;
    }
    
    try {
        // Start transaction
        dbQuery("START TRANSACTION");
        
        // Add income to user's main wallet
        $wallet_query = dbQuery("SELECT * FROM tabl_wallet WHERE user_id = '$user_id' AND user_type = 1");
        if (dbNumRows($wallet_query) > 0) {
            // Update existing wallet
            dbQuery("UPDATE tabl_wallet SET 
                amount = amount + '$total_income',
                date = NOW() 
                WHERE user_id = '$user_id' AND user_type = 1
            ");
        } else {
            // Create new wallet entry
            dbQuery("INSERT INTO tabl_wallet SET 
                user_id = '$user_id',
                user_type = '1',
                amount = '$total_income',
                status = '1',
                date = NOW()
            ");
        }
        
        // Log the income distribution in wallet summary
        dbQuery("INSERT INTO tabl_walletsummery SET 
            user_id = '$user_id',
            user_type = '1',
            order_id = 'DAILY_INCOME_" . date('Ymd') . "_$user_id',
            amount = '$total_income',
            type = 'credit',
            actiontype = 'daily_rank_income',
            date = NOW()
        ");
        
        // Add notification
        dbQuery("INSERT INTO tabl_notification SET 
            user_id = '$user_id',
            type = 'daily_income',
            title = 'Daily Rank Income Received',
            description = 'You received ₹$total_income as daily rank income for $rank_name rank. Breakdown: $breakdown',
            data = '$total_income',
            status = '0',
            date = NOW()
        ");
        
        // Commit transaction
        dbQuery("COMMIT");
        
        logActivity("SUCCESS: User $user_name (ID: $user_id) received ₹$total_income daily income for rank $rank_name. Breakdown: $breakdown");
        return true;
        
    } catch (Exception $e) {
        // Rollback transaction on error
        dbQuery("ROLLBACK");
        logActivity("ERROR: Failed to distribute daily income to user $user_name (ID: $user_id). Error: " . $e->getMessage());
        return false;
    }
}

// Main execution starts here
logActivity("=== CRON JOB STARTED: Daily Income Distribution ===");

$today = date('Y-m-d');
logActivity("Processing date: $today");

// Get all users with active subscriptions and ranks
$users_query = dbQuery("
    SELECT u.id, u.name, u.email, u.current_rank_id, r.name as rank_name, r.daily_credit
    FROM tabl_user u
    LEFT JOIN tabl_ranks r ON u.current_rank_id = r.id
    WHERE u.id > 1 
    AND u.current_rank_id > 0
    ORDER BY u.id ASC
");

$total_users_checked = 0;
$users_with_active_subscription = 0;
$income_distributed = 0;
$users_skipped = 0;
$total_amount_distributed = 0;

logActivity("Found " . dbNumRows($users_query) . " users with ranks to check");

while ($user = dbFetchAssoc($users_query)) {
    $user_id = $user['id'];
    $user_name = $user['name'];
    $current_rank_id = $user['current_rank_id'];
    $rank_name = $user['rank_name'];
    
    $total_users_checked++;
    
    // Check if user has active subscription
    if (!hasActiveSubscription($user_id)) {
        logActivity("SKIP: User $user_name (ID: $user_id) has no active subscription");
        $users_skipped++;
        continue;
    }
    
    $users_with_active_subscription++;
    
    // Calculate current rank daily income
    $income_data = calculateCurrentRankDailyIncome($current_rank_id);

    logActivity("Processing user $user_name (ID: $user_id) - Rank: $rank_name (ID: $current_rank_id), Current Rank Income: ₹{$income_data['total_income']}");
    
    // Distribute daily income
    if (distributeDailyIncome($user_id, $user_name, $current_rank_id, $rank_name, $income_data)) {
        $income_distributed++;
        $total_amount_distributed += $income_data['total_income'];
    } else {
        $users_skipped++;
    }
}

// Summary
logActivity("=== CRON JOB COMPLETED ===");
logActivity("Summary:");
logActivity("- Total users checked: $total_users_checked");
logActivity("- Users with active subscriptions: $users_with_active_subscription");
logActivity("- Income distributed to: $income_distributed users");
logActivity("- Users skipped: $users_skipped");
logActivity("- Total amount distributed: ₹$total_amount_distributed");
logActivity("- Date processed: $today");
logActivity("=== END OF LOG ===");

// Optional: Send email notification to admin
if ($income_distributed > 0) {
    $subject = "Daily Income Distribution Report - $today";
    $message = "Daily income distributed to $income_distributed users. Total amount: ₹$total_amount_distributed. Check logs for details.";
    
    // Uncomment and configure if you want email notifications
    // mail('<EMAIL>', $subject, $message);
}

?>
