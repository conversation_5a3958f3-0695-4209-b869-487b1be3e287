<?php
/**
 * Setup and Test Script for Daily Income Cron Job
 * 
 * This script helps you:
 * 1. Test the daily income distribution
 * 2. View income calculation breakdown
 * 3. Monitor distribution logs
 * 4. Get cron job setup instructions
 */

include_once('admin/lib/db_connection.php');
date_default_timezone_set("Asia/Kolkata");

// Handle AJAX requests
if (isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    switch ($_POST['action']) {
        case 'test_income_cron':
            // Execute the daily income cron job manually
            ob_start();
            include_once('cron_daily_income.php');
            $output = ob_get_clean();
            
            echo json_encode([
                'status' => 'success',
                'message' => 'Daily income cron job executed successfully',
                'output' => $output
            ]);
            exit;
            
        case 'get_income_logs':
            $log_file = 'logs/cron_daily_income_' . date('Y-m') . '.log';
            if (file_exists($log_file)) {
                $logs = file_get_contents($log_file);
                echo json_encode([
                    'status' => 'success',
                    'logs' => $logs
                ]);
            } else {
                echo json_encode([
                    'status' => 'error',
                    'message' => 'No log file found for this month'
                ]);
            }
            exit;
            
        case 'calculate_user_income':
            $user_id = intval($_POST['user_id']);
            
            // Get user details
            $user_query = dbQuery("
                SELECT u.id, u.name, u.current_rank_id, r.name as rank_name
                FROM tabl_user u
                LEFT JOIN tabl_ranks r ON u.current_rank_id = r.id
                WHERE u.id = '$user_id'
            ");
            
            if (dbNumRows($user_query) > 0) {
                $user = dbFetchAssoc($user_query);
                
                // Calculate current rank income only
                $rank_query = dbQuery("
                    SELECT id, name, daily_credit
                    FROM tabl_ranks
                    WHERE id = '{$user['current_rank_id']}'
                ");

                $breakdown = [];
                $total = 0;

                if (dbNumRows($rank_query) > 0) {
                    $rank = dbFetchAssoc($rank_query);
                    $daily_credit = floatval($rank['daily_credit']);
                    $total = $daily_credit;
                    $breakdown[] = [
                        'rank_name' => $rank['name'],
                        'daily_credit' => $daily_credit
                    ];
                }
                
                echo json_encode([
                    'status' => 'success',
                    'user' => $user,
                    'breakdown' => $breakdown,
                    'total_income' => $total
                ]);
            } else {
                echo json_encode([
                    'status' => 'error',
                    'message' => 'User not found'
                ]);
            }
            exit;
    }
}

// Get users for testing
$users_query = dbQuery("
    SELECT u.id, u.name, u.email, u.current_rank_id, r.name as rank_name, r.daily_credit
    FROM tabl_user u
    LEFT JOIN tabl_ranks r ON u.current_rank_id = r.id
    WHERE u.id > 1 AND u.current_rank_id > 0
    ORDER BY u.current_rank_id DESC, u.id ASC
    LIMIT 20
");

// Get all ranks for reference
$ranks_query = dbQuery("SELECT * FROM tabl_ranks ORDER BY id ASC");
?>

<!doctype html>
<html class="no-js" lang="">

<head>
    <meta charset="utf-8">
    <meta http-equiv="x-ua-compatible" content="ie=edge">
    <title>Daily Income Cron Setup & Test</title>
    <meta name="description" content="Setup and test daily income distribution">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font-awesome CSS-->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">

    <style>
        .setup-card {
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .cron-command {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }
        .log-output {
            background: #000;
            color: #00ff00;
            font-family: 'Courier New', monospace;
            font-size: 0.8em;
            height: 300px;
            overflow-y: auto;
            padding: 10px;
            border-radius: 5px;
        }
        .income-breakdown {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
        }
    </style>
</head>

<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h1 class="mb-4">
                    <i class="fas fa-coins text-success"></i> Daily Income Distribution Setup & Test
                </h1>
                
                <!-- Cron Setup Instructions -->
                <div class="card setup-card mb-4">
                    <div class="card-header bg-success text-white">
                        <h3 class="mb-0">
                            <i class="fas fa-clock"></i> Cron Job Setup Instructions
                        </h3>
                    </div>
                    <div class="card-body">
                        <h5>1. Server Cron Job Setup</h5>
                        <p>Add this line to your server's crontab to run daily at 6:00 AM:</p>
                        <div class="cron-command">
                            0 6 * * * /usr/bin/php <?= realpath('cron_daily_income.php') ?> >> <?= realpath('logs') ?>/cron_income_output.log 2>&1
                        </div>
                        
                        <h5 class="mt-4">2. Alternative Schedules</h5>
                        <ul>
                            <li><code>0 8 * * *</code> - Run at 8:00 AM daily</li>
                            <li><code>0 12 * * *</code> - Run at 12:00 PM daily</li>
                            <li><code>0 18 * * *</code> - Run at 6:00 PM daily</li>
                            <li><code>*/10 * * * *</code> - Run every 10 minutes (for testing only)</li>
                        </ul>
                        
                        <h5 class="mt-4">3. Manual Execution</h5>
                        <p>You can also run the cron job manually:</p>
                        <div class="cron-command">
                            php <?= realpath('cron_daily_income.php') ?>
                        </div>
                    </div>
                </div>

                <!-- Income Calculation Demo -->
                <div class="card setup-card mb-4">
                    <div class="card-header bg-info text-white">
                        <h3 class="mb-0">
                            <i class="fas fa-calculator"></i> Income Calculation Demo
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <label for="demo_user_select" class="form-label">Select User to Calculate Income:</label>
                                <select class="form-select" id="demo_user_select" onchange="calculateUserIncome()">
                                    <option value="">Select a user...</option>
                                    <?php while ($user = dbFetchAssoc($users_query)): ?>
                                        <option value="<?= $user['id'] ?>">
                                            <?= htmlspecialchars($user['name']) ?> (ID: <?= $user['id'] ?>) - 
                                            Rank: <?= $user['rank_name'] ?: 'None' ?> - 
                                            Base Income: ₹<?= $user['daily_credit'] ?: 0 ?>
                                        </option>
                                    <?php endwhile; ?>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Action:</label>
                                <button class="btn btn-info w-100" onclick="calculateUserIncome()">
                                    <i class="fas fa-calculator"></i> Calculate Income
                                </button>
                            </div>
                        </div>
                        
                        <div id="income_breakdown" class="mt-4" style="display: none;">
                            <!-- Will be populated by JavaScript -->
                        </div>
                    </div>
                </div>

                <!-- Test Controls -->
                <div class="card setup-card mb-4">
                    <div class="card-header bg-warning text-dark">
                        <h3 class="mb-0">
                            <i class="fas fa-play"></i> Test Controls
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <button class="btn btn-success btn-lg w-100 mb-3" onclick="testIncomeCron()">
                                    <i class="fas fa-play"></i> Run Income Distribution
                                </button>
                            </div>
                            <div class="col-md-4">
                                <button class="btn btn-info btn-lg w-100 mb-3" onclick="viewIncomeLogs()">
                                    <i class="fas fa-file-alt"></i> View Logs
                                </button>
                            </div>
                            <div class="col-md-4">
                                <button class="btn btn-secondary btn-lg w-100 mb-3" onclick="clearOutput()">
                                    <i class="fas fa-eraser"></i> Clear Output
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Output Display -->
                <div class="card setup-card mb-4">
                    <div class="card-header bg-dark text-white">
                        <h3 class="mb-0">
                            <i class="fas fa-terminal"></i> Output & Logs
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="log-output" id="output_display">
                            <div class="text-center text-muted">
                                Click "Run Income Distribution" or "View Logs" to see output here...
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Rank Reference -->
                <div class="card setup-card mb-4">
                    <div class="card-header bg-secondary text-white">
                        <h3 class="mb-0">
                            <i class="fas fa-list"></i> Rank Income Reference
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Rank ID</th>
                                        <th>Name</th>
                                        <th>Daily Credit</th>
                                        <th>Cumulative Income</th>
                                        <th>Example Calculation</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php 
                                    $cumulative = 0;
                                    $calculation_parts = [];
                                    while ($rank = dbFetchAssoc($ranks_query)): 
                                        $cumulative += $rank['daily_credit'];
                                        $calculation_parts[] = "₹{$rank['daily_credit']}";
                                        $calculation_example = implode(' + ', $calculation_parts) . " = ₹{$cumulative}";
                                    ?>
                                        <tr>
                                            <td><?= $rank['id'] ?></td>
                                            <td><strong><?= htmlspecialchars($rank['name']) ?></strong></td>
                                            <td>₹<?= number_format($rank['daily_credit'], 2) ?></td>
                                            <td><strong>₹<?= number_format($cumulative, 2) ?></strong></td>
                                            <td><small><?= $calculation_example ?></small></td>
                                        </tr>
                                    <?php endwhile; ?>
                                </tbody>
                            </table>
                        </div>
                        <div class="alert alert-info mt-3">
                            <strong>How it works:</strong> Users receive cumulative income from rank 1 up to their current rank. 
                            For example, a user with rank 3 receives the daily income of rank 1 + rank 2 + rank 3.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <script>
        function testIncomeCron() {
            updateOutput('Starting daily income distribution...\n');
            
            $.ajax({
                url: 'setup_cron_daily_income.php',
                type: 'POST',
                data: { action: 'test_income_cron' },
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'success') {
                        updateOutput(response.output);
                    } else {
                        updateOutput('Error: ' + response.message);
                    }
                },
                error: function() {
                    updateOutput('Error executing daily income cron job');
                }
            });
        }

        function viewIncomeLogs() {
            $.ajax({
                url: 'setup_cron_daily_income.php',
                type: 'POST',
                data: { action: 'get_income_logs' },
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'success') {
                        updateOutput(response.logs);
                    } else {
                        updateOutput('Error: ' + response.message);
                    }
                },
                error: function() {
                    updateOutput('Error loading logs');
                }
            });
        }

        function calculateUserIncome() {
            const userId = document.getElementById('demo_user_select').value;
            if (!userId) {
                document.getElementById('income_breakdown').style.display = 'none';
                return;
            }

            $.ajax({
                url: 'setup_cron_daily_income.php',
                type: 'POST',
                data: { 
                    action: 'calculate_user_income',
                    user_id: userId 
                },
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'success') {
                        displayIncomeBreakdown(response);
                    } else {
                        alert('Error: ' + response.message);
                    }
                },
                error: function() {
                    alert('Error calculating user income');
                }
            });
        }

        function displayIncomeBreakdown(data) {
            let html = `
                <div class="income-breakdown">
                    <h5>Income Calculation for ${data.user.name} (Rank: ${data.user.rank_name})</h5>
                    <div class="row">
                        <div class="col-md-8">
                            <h6>Breakdown:</h6>
                            <ul>
            `;
            
            data.breakdown.forEach(function(rank) {
                html += `<li>${rank.rank_name}: ₹${rank.daily_credit}</li>`;
            });
            
            html += `
                            </ul>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <h4 class="text-success">Total Daily Income</h4>
                                <h2 class="text-success">₹${data.total_income}</h2>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            document.getElementById('income_breakdown').innerHTML = html;
            document.getElementById('income_breakdown').style.display = 'block';
        }

        function updateOutput(text) {
            const output = document.getElementById('output_display');
            output.innerHTML = '<pre>' + text + '</pre>';
            output.scrollTop = output.scrollHeight;
        }

        function clearOutput() {
            document.getElementById('output_display').innerHTML = '<div class="text-center text-muted">Output cleared...</div>';
        }
    </script>
</body>

</html>
