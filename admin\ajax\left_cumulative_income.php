<?php
session_start();
include('lib/db_connection.php');
include('lib/get_functions.php');
include('lib/auth.php');
date_default_timezone_set("Asia/Kolkata");

// Set page variables for menu highlighting
$page = 10;
$sub_page = 105;

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');

    switch ($_POST['action']) {
        case 'update_left_amount':
            $user_id = intval($_POST['user_id']);
            $rank_id = intval($_POST['rank_id']);
            $new_amount = floatval($_POST['new_amount']);

            if ($new_amount < 0) {
                echo json_encode(['status' => 'error', 'message' => 'Amount cannot be negative']);
                exit;
            }

            // Store the updated amount in session or database for later distribution
            if (!isset($_SESSION['left_income_updates'])) {
                $_SESSION['left_income_updates'] = [];
            }

            $_SESSION['left_income_updates'][$user_id][$rank_id] = $new_amount;

            echo json_encode(['status' => 'success', 'message' => 'Amount updated successfully']);
            exit;

        case 'get_user_left_income':
            $user_id = intval($_POST['user_id']);

            // Get user details
            $user_query = dbQuery("
                SELECT u.id, u.name, u.email, u.current_rank_id, r.name as current_rank_name,
                       u.date_added as registration_date
                FROM tabl_user u
                LEFT JOIN tabl_ranks r ON u.current_rank_id = r.id
                WHERE u.id = '$user_id'
            ");

            if (dbNumRows($user_query) == 0) {
                echo json_encode(['status' => 'error', 'message' => 'User not found']);
                exit;
            }

            $user = dbFetchAssoc($user_query);
            $current_rank_id = $user['current_rank_id'];

            if ($current_rank_id <= 1) {
                echo json_encode(['status' => 'error', 'message' => 'User has no previous ranks to calculate left income']);
                exit;
            }

            // Calculate left income for each previous rank
            $left_income_data = [];

            // Get all ranks below current rank
            $previous_ranks_query = dbQuery("
                SELECT id, name, daily_credit
                FROM tabl_ranks
                WHERE id < '$current_rank_id'
                ORDER BY id ASC
            ");

            while ($rank = dbFetchAssoc($previous_ranks_query)) {
                $rank_id = $rank['id'];
                $rank_name = $rank['name'];
                $daily_credit = $rank['daily_credit'];

                // Count days user received income from this rank (old cumulative system)
                $received_days_query = dbQuery("
                    SELECT COUNT(DISTINCT DATE(date)) as received_days
                    FROM tabl_walletsummery
                    WHERE user_id = '$user_id'
                    AND actiontype = 'daily_rank_income'
                    AND amount >= '$daily_credit'
                ");

                $received_days_result = dbFetchAssoc($received_days_query);
                $received_days = $received_days_result['received_days'] ?: 0;

                // Count days user received left cumulative income for this rank
                $left_income_received_query = dbQuery("
                    SELECT COUNT(*) as left_received_days
                    FROM tabl_walletsummery
                    WHERE user_id = '$user_id'
                    AND actiontype = 'left_cumulative_income'
                    AND order_id LIKE '%RANK_{$rank_id}_%'
                ");

                $left_received_result = dbFetchAssoc($left_income_received_query);
                $left_received_days = $left_received_result['left_received_days'] ?: 0;

                // Calculate total possible days (from registration to current date)
                $total_possible_days_query = dbQuery("
                    SELECT DATEDIFF(CURDATE(), DATE(u.date_added)) + 1 as total_days
                    FROM tabl_user u
                    WHERE u.id = '$user_id'
                ");

                $total_days_result = dbFetchAssoc($total_possible_days_query);
                $total_possible_days = $total_days_result['total_days'] ?: 0;

                // Adjust received days to exclude already distributed left income
                $actual_received_days = $received_days - $left_received_days;

                // Calculate left days and amount (daily income calculation)
                $left_days = max(0, $total_possible_days - $actual_received_days);
                $left_amount = $left_days * $daily_credit;

                // Check if admin has updated this amount
                $updated_amount = $_SESSION['left_income_updates'][$user_id][$rank_id] ?? $left_amount;

                $left_income_data[] = [
                    'rank_id' => $rank_id,
                    'rank_name' => $rank_name,
                    'daily_credit' => $daily_credit,
                    'received_days' => $actual_received_days,
                    'left_received_days' => $left_received_days,
                    'total_possible_days' => $total_possible_days,
                    'left_days' => $left_days,
                    'left_amount' => $left_amount,
                    'updated_amount' => $updated_amount
                ];
            }

            echo json_encode([
                'status' => 'success',
                'user' => $user,
                'left_income_data' => $left_income_data
            ]);
            exit;

        case 'check_rank_reset_status':
            $user_id = intval($_POST['user_id']);

            // Check if user has active subscription using correct table name
            $subscription_query = dbQuery("
                SELECT s.*, p.duration_days,
                       DATE_ADD(s.subscribed_at, INTERVAL COALESCE(p.duration_days, 30) DAY) as expiry_date,
                       DATEDIFF(DATE_ADD(s.subscribed_at, INTERVAL COALESCE(p.duration_days, 30) DAY), CURDATE()) as days_until_reset
                FROM tabl_user_subscriptions s
                LEFT JOIN tabl_subscription_packages p ON s.package_id = p.id
                WHERE s.user_id = '$user_id'
                ORDER BY s.subscribed_at DESC
                LIMIT 1
            ");

            if (dbNumRows($subscription_query) == 0) {
                echo json_encode([
                    'status' => 'success',
                    'days_until_reset' => 999,
                    'expiry_date' => 'No subscription found',
                    'can_distribute' => true,
                    'message' => 'No subscription found - allowing distribution'
                ]);
                exit;
            }

            $subscription = dbFetchAssoc($subscription_query);
            $days_until_reset = $subscription['days_until_reset'] ?: 999;

            echo json_encode([
                'status' => 'success',
                'days_until_reset' => $days_until_reset,
                'expiry_date' => $subscription['expiry_date'],
                'can_distribute' => $days_until_reset > 0
            ]);
            exit;

        case 'distribute_left_income':
            $user_id = intval($_POST['user_id']);
            $rank_id = intval($_POST['rank_id']);
            $amount = floatval($_POST['amount']);
            $days = intval($_POST['days']) ?: 1;

            // Use updated amount if admin has modified it
            if (isset($_SESSION['left_income_updates'][$user_id][$rank_id])) {
                $amount = $_SESSION['left_income_updates'][$user_id][$rank_id];
            }

            if ($amount <= 0) {
                echo json_encode(['status' => 'error', 'message' => 'Invalid amount']);
                exit;
            }

            // Check if distribution is allowed (before rank reset) - simplified for now
            // Admin can distribute at any time during subscription month
            $can_distribute = true; // Allow distribution for now

            // Get user and rank details
            $user_query = dbQuery("SELECT name FROM tabl_user WHERE id = '$user_id'");
            $rank_query = dbQuery("SELECT name FROM tabl_ranks WHERE id = '$rank_id'");

            if (dbNumRows($user_query) == 0 || dbNumRows($rank_query) == 0) {
                echo json_encode(['status' => 'error', 'message' => 'User or rank not found']);
                exit;
            }

            $user = dbFetchAssoc($user_query);
            $rank = dbFetchAssoc($rank_query);

            try {
                // Start transaction
                dbQuery("START TRANSACTION");

                // Add to user wallet
                $wallet_query = dbQuery("SELECT * FROM tabl_wallet WHERE user_id = '$user_id' AND user_type = 1");
                if (dbNumRows($wallet_query) > 0) {
                    dbQuery("UPDATE tabl_wallet SET 
                        amount = amount + '$amount',
                        date = NOW() 
                        WHERE user_id = '$user_id' AND user_type = 1
                    ");
                } else {
                    dbQuery("INSERT INTO tabl_wallet SET 
                        user_id = '$user_id',
                        user_type = '1',
                        amount = '$amount',
                        status = '1',
                        date = NOW()
                    ");
                }

                // Log in wallet summary
                $order_id = 'LEFT_CUMULATIVE_RANK_' . $rank_id . '_' . date('Ymd_His') . '_' . $user_id;
                dbQuery("INSERT INTO tabl_walletsummery SET 
                    user_id = '$user_id',
                    user_type = '1',
                    order_id = '$order_id',
                    amount = '$amount',
                    type = 'credit',
                    actiontype = 'left_cumulative_income',
                    date = NOW()
                ");

                // Add notification
                dbQuery("INSERT INTO tabl_notification SET
                    user_id = '$user_id',
                    type = 'left_income',
                    title = 'Left Cumulative Income Received',
                    description = 'You received ₹$amount as left cumulative income from {$rank['name']} rank.',
                    data = '$amount',
                    status = '0',
                    date = NOW()
                ");

                // Log admin action (if admin_logs table exists)
                $admin_id = $_SESSION['admin_id'] ?? 1;
                $admin_logs_check = dbQuery("SHOW TABLES LIKE 'tabl_admin_logs'");
                if (dbNumRows($admin_logs_check) > 0) {
                    dbQuery("INSERT INTO tabl_admin_logs SET
                        admin_id = '$admin_id',
                        action = 'distribute_left_income',
                        description = 'Distributed ₹$amount left cumulative income from {$rank['name']} rank to {$user['name']} (ID: $user_id)',
                        date = NOW()
                    ");
                }

                dbQuery("COMMIT");

                echo json_encode([
                    'status' => 'success',
                    'message' => "Successfully distributed ₹$amount to {$user['name']}"
                ]);
            } catch (Exception $e) {
                dbQuery("ROLLBACK");
                echo json_encode(['status' => 'error', 'message' => 'Failed to distribute income: ' . $e->getMessage()]);
            }
            exit;
    }
}
