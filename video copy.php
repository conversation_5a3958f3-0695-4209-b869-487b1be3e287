<?php
session_start();
include('admin/lib/db_connection.php');
include('lib/translation_helper.php');
date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d H:i:s');


?>


<!doctype html>
<html class="no-js" lang="">



<head>
    <base href="<?= BASE_URL; ?>">
    <meta charset="utf-8">
    <meta http-equiv="x-ua-compatible" content="ie=edge">
    <title>Win TV 99</title>
    <meta name="description"
        content="Win TV 99 is your go-to destination for the latest news, updates, and insightful stories from across India and around the globe. We are committed to delivering accurate, reliable, and unbiased journalism that empowers our readers with knowledge and understanding">
    <meta name="keywords" content="Spiritual,travel,story,Innovation,stay,news">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!-- Favicon -->
    <link rel="shortcut icon" type="image/x-icon" href="img/favicon2.png">
    <!-- Normalize CSS -->
    <link rel="stylesheet" href="css/normalize.css">
    <!-- Main CSS -->
    <link rel="stylesheet" href="css/main.css">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" rel="stylesheet"
        integrity="sha384-EVSTQN3/azprG1Anm3QDgpJLIm9Nao0Yz1ztcQTwFspd3yD65VohhpuuCOmLASjC" crossorigin="anonymous">
    <link rel="stylesheet" href="css/bootstrap.min.css">

    <!-- Animate CSS -->
    <link rel="stylesheet" href="css/animate.min.css">
    <!-- Font-awesome CSS-->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="css/font-awesome.min.css">
    <!-- Owl Caousel CSS -->
    <link rel="stylesheet" href="vendor/OwlCarousel/owl.carousel.min.css">
    <link rel="stylesheet" href="vendor/OwlCarousel/owl.theme.default.min.css">
    <!-- Main Menu CSS -->
    <link rel="stylesheet" href="css/meanmenu.min.css">
    <!-- Nivo Slider CSS-->
    <link rel="stylesheet" href="vendor/slider/css/nivo-slider.css" type="text/css" />
    <link rel="stylesheet" href="vendor/slider/css/preview.css" type="text/css" media="screen" />
    <!-- Magnific CSS -->
    <link rel="stylesheet" type="text/css" href="css/magnific-popup.css">
    <!-- Switch Style CSS -->
    <link rel="stylesheet" href="css/hover-min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="style.css">

    <!-- For IE -->
    <link rel="stylesheet" type="text/css" href="css/ie-only.css" />
    <link rel="stylesheet" href="css/me.css">
    <link rel="stylesheet" href="css/custom.css">
    <link rel="stylesheet" href="css/lightfooter.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <!-- Modernizr Js -->
    <script src="js/modernizr-2.8.3.min.js"></script>
    <style>
        section.bg-accent-dark:nth-child(odd) {
            background: #f6f6f6;
        }

        .bg-accent-dark {
            padding: 20px 0;
        }
    </style>
</head>

<body>
    <div id="main">

        <div id="wrapper">
            <?php include("./inc/header_mobile.php"); ?>

            <?php include("./inc/header.php"); ?>

            <div class="d-lg-none d-block" style="padding-top: 50px;"> </div>
            <div class="d-none d-lg-block" style="padding-top: 130px;"> </div>



            <!-- Header Area End Here -->


            <!-- Feature News Area Start Here -->
            <section class="section-space-less30" id="video">
                <div class="container">

                    <div class="row tab-space5">
                        <h1 class="cat-heading text-left">Video</h1>
                        <div class="col-lg-8 col-md-12 mb-10">


                            <div class="row no-gutters mt-5">
                                <video class="video width-100" width="180" controls="">
                                    <source src="./assets/video/605890.mp4" type="video/mp4">
                                    Your browser does not support the video tag.
                                </video>
                            </div>

                        </div>
                        <div class="col-lg-4 col-md-12">
                            <div class="row mt-5 tab-space5">

                                <div class="col-12">
                                    <a href="https://www.indiatvnews.com/video/astrology/shubh-muhurat-today-bhavishyavani-with-acharya-indu-prakash-astro-2025-05-30-992539" title="Aaj Ka Rashifal, 30 May, 2025: Shubh Muhurat | Today Bhavishyavani with Acharya Indu Prakash | Astro" class="next-video">
                                        <span class="thumb">
                                            <figure>
                                                <img src="https://resize.indiatvnews.com/resize/300_-/2025/05/gmy2xf1w.jpg" data-original="https://resize.indiatvnews.com/resize/300_-/2025/05/gmy2xf1w.jpg" alt="Aaj Ka Rashifal, 30 May, 2025: Shubh Muhurat | Today Bhavishyavani with Acharya Indu Prakash | Astro" class="lazy" width="298" height="168" style="">
                                            </figure>
                                            <span class="icon video-paly"></span>
                                        </span>
                                        <p class="caption">Aaj Ka Rashifal, 30 May, 2025: Shubh Muhurat | Today Bhavishyavani with Acharya Indu Prakash | Astro</p>
                                    </a>

                                </div>

                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section>
                <div class="container">
                    <div class="row tab-space5">


                        <div class="col-lg-3 col-md-12">
                            <div class="row mt-2 tab-space5">

                                <div class="col-12">
                                    <a href="https://www.indiatvnews.com/video/astrology/shubh-muhurat-today-bhavishyavani-with-acharya-indu-prakash-astro-2025-05-30-992539" title="Aaj Ka Rashifal, 30 May, 2025: Shubh Muhurat | Today Bhavishyavani with Acharya Indu Prakash | Astro" class="next-video">
                                        <span class="thumb">
                                            <figure>
                                                <img src="https://resize.indiatvnews.com/resize/300_-/2025/05/gmy2xf1w.jpg" data-original="https://resize.indiatvnews.com/resize/300_-/2025/05/gmy2xf1w.jpg" alt="Aaj Ka Rashifal, 30 May, 2025: Shubh Muhurat | Today Bhavishyavani with Acharya Indu Prakash | Astro" class="lazy" width="298" height="168" style="">
                                            </figure>
                                            <span class="icon video-paly"></span>
                                        </span>
                                        <p class="caption">Aaj Ka Rashifal, 30 May, 2025: Shubh Muhurat | Today Bhavishyavani with Acharya Indu Prakash | Astro</p>
                                    </a>

                                </div>

                            </div>
                        </div>
                        <div class="col-lg-3 col-md-12">
                            <div class="row mt-2 tab-space5">

                                <div class="col-12">
                                    <a href="https://www.indiatvnews.com/video/astrology/shubh-muhurat-today-bhavishyavani-with-acharya-indu-prakash-astro-2025-05-30-992539" title="Aaj Ka Rashifal, 30 May, 2025: Shubh Muhurat | Today Bhavishyavani with Acharya Indu Prakash | Astro" class="next-video">
                                        <span class="thumb">
                                            <figure>
                                                <img src="https://resize.indiatvnews.com/resize/300_-/2025/05/gmy2xf1w.jpg" data-original="https://resize.indiatvnews.com/resize/300_-/2025/05/gmy2xf1w.jpg" alt="Aaj Ka Rashifal, 30 May, 2025: Shubh Muhurat | Today Bhavishyavani with Acharya Indu Prakash | Astro" class="lazy" width="298" height="168" style="">
                                            </figure>
                                            <span class="icon video-paly"></span>
                                        </span>
                                        <p class="caption">Aaj Ki Baat: What warning did PM Modi give again to Pakistan ?</p>
                                    </a>

                                </div>

                            </div>
                        </div>
                        <div class="col-lg-3 col-md-12">
                            <div class="row mt-2 tab-space5">

                                <div class="col-12">
                                    <a href="https://www.indiatvnews.com/video/astrology/shubh-muhurat-today-bhavishyavani-with-acharya-indu-prakash-astro-2025-05-30-992539" title="Aaj Ka Rashifal, 30 May, 2025: Shubh Muhurat | Today Bhavishyavani with Acharya Indu Prakash | Astro" class="next-video">
                                        <span class="thumb">
                                            <figure>
                                                <img src="https://resize.indiatvnews.com/resize/300_-/2025/05/gmy2xf1w.jpg" data-original="https://resize.indiatvnews.com/resize/300_-/2025/05/gmy2xf1w.jpg" alt="Aaj Ka Rashifal, 30 May, 2025: Shubh Muhurat | Today Bhavishyavani with Acharya Indu Prakash | Astro" class="lazy" width="298" height="168" style="">
                                            </figure>
                                            <span class="icon video-paly"></span>
                                        </span>
                                        <p class="caption">Mamata slams PM Modi's Bengal remarks as saddening during anti-terror diplomacy | 29 May | Speed News</p>
                                    </a>

                                </div>

                            </div>
                        </div>
                        <div class="col-lg-3 col-md-12">
                            <div class="row mt-2 tab-space5">

                                <div class="col-12">
                                    <a href="https://www.indiatvnews.com/video/astrology/shubh-muhurat-today-bhavishyavani-with-acharya-indu-prakash-astro-2025-05-30-992539" title="Aaj Ka Rashifal, 30 May, 2025: Shubh Muhurat | Today Bhavishyavani with Acharya Indu Prakash | Astro" class="next-video">
                                        <span class="thumb">
                                            <figure>
                                                <img src="https://resize.indiatvnews.com/resize/300_-/2025/05/gmy2xf1w.jpg" data-original="https://resize.indiatvnews.com/resize/300_-/2025/05/gmy2xf1w.jpg" alt="Aaj Ka Rashifal, 30 May, 2025: Shubh Muhurat | Today Bhavishyavani with Acharya Indu Prakash | Astro" class="lazy" width="298" height="168" style="">
                                            </figure>
                                            <span class="icon video-paly"></span>
                                        </span>
                                        <p class="caption">As Elon Musk Exits Trump Administration, A Look At How Frenemies Turned Into Close Allies</p>
                                    </a>

                                </div>

                            </div>
                        </div>


                    </div>
                </div>
            </section>
            <section class="bg-accent-dark">

                <div class="container">
                    <div class="row">
                        <div class="col-lg-12">
                            <h2 class="cat-heading d-lg-block text-left">
                                Latest Videos
                            </h2>

                            <div class="row mt-5">
                                <?php
                                $sel_news = dbQuery("SELECT * FROM tabl_post WHERE video!='' OR video_link!='' ORDER BY id DESC LIMIT 4");
                                ?>

                                <div class="col-md-12 col-sm-12">
                                    <div class="row">


                                        <div class="latest-carousel owl-carousel owl-theme">

                                            <?php
                                            // $sel_news = dbQuery("SELECT * FROM tabl_post WHERE video!='' OR video_link!='' ORDER BY headline ASC LIMIT 0,10");
                                            while ($res_news = dbFetchAssoc($sel_news)) {

                                                $cat_details = dbQuery("SELECT tabl_post_to_category.*,tabl_category.category,tabl_category.slug as catslug FROM tabl_post_to_category INNER JOIN tabl_category ON tabl_post_to_category.category_id=tabl_category.id WHERE tabl_post_to_category.post_id='" . $res_news['id'] . "'");
                                                $res_cat_details = dbFetchAssoc($cat_details);
                                            ?>

                                                <a href="<?php echo BASE_URL . '' . $res_cat_details['catslug'] . '/' . $res_news['slug']; ?>.html" title="<?php echo $res_news['headline'] . " : " . $res_news['title']; ?>" class="next-video">
                                                    <span class="thumb">
                                                        <figure>
                                                            <img src="./assets/post/<?php echo $res_news['image']; ?>" alt="<?php echo $res_news['headline'] . " : " . $res_news['title']; ?>" class="lazy" width="298" height="168" style="">
                                                        </figure>
                                                        <span class="icon video-paly"></span>
                                                    </span>
                                                    <p class="caption"><?php echo $res_news['headline'] . " : " . $res_news['title']; ?></p>
                                                </a>
                                            <?php
                                            }
                                            ?>
                                        </div>

                                        <div class="view text-center mt-5">
                                            <a href="http://localhost/wintv99_new/category/travells" class="viewall">View All <i class="fa fa-long-arrow-right" aria-hidden="true"></i></a>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </section>

            <section class="bg-accent-dark">

                <div class="container">
                    <div class="row">
                        <div class="col-lg-12">
                            <h2 class="cat-heading d-lg-block text-left">
                                Aap ki Adalat
                            </h2>

                            <div class="row mt-5">
                                <?php
                                $sel_news = dbQuery("SELECT * FROM tabl_post WHERE video!='' OR video_link!='' ORDER BY id DESC LIMIT 4");
                                ?>

                                <div class="col-md-12 col-sm-12">
                                    <div class="row">


                                        <div class="latest-carousel owl-carousel owl-theme">

                                            <?php
                                            // $sel_news = dbQuery("SELECT * FROM tabl_post WHERE video!='' OR video_link!='' ORDER BY headline ASC LIMIT 0,10");
                                            while ($res_news = dbFetchAssoc($sel_news)) {

                                                $cat_details = dbQuery("SELECT tabl_post_to_category.*,tabl_category.category,tabl_category.slug as catslug FROM tabl_post_to_category INNER JOIN tabl_category ON tabl_post_to_category.category_id=tabl_category.id WHERE tabl_post_to_category.post_id='" . $res_news['id'] . "'");
                                                $res_cat_details = dbFetchAssoc($cat_details);
                                            ?>

                                                <a href="<?php echo BASE_URL . '' . $res_cat_details['catslug'] . '/' . $res_news['slug']; ?>.html" title="<?php echo $res_news['headline'] . " : " . $res_news['title']; ?>" class="next-video">
                                                    <span class="thumb">
                                                        <figure>
                                                            <img src="./assets/post/<?php echo $res_news['image']; ?>" alt="<?php echo $res_news['headline'] . " : " . $res_news['title']; ?>" class="lazy" width="298" height="168" style="">
                                                        </figure>
                                                        <span class="icon video-paly"></span>
                                                    </span>
                                                    <p class="caption"><?php echo $res_news['headline'] . " : " . $res_news['title']; ?></p>
                                                </a>
                                            <?php
                                            }
                                            ?>
                                        </div>
                                    </div>

                                </div>
                            </div>

                            <div class="view text-center mt-5">
                                <a href="http://localhost/wintv99_new/category/travells" class="viewall">View All <i class="fa fa-long-arrow-right" aria-hidden="true"></i></a>
                            </div>
                        </div>
                    </div>

                </div>
            </section>

            <section class="bg-accent-dark">

                <div class="container">
                    <div class="row">
                        <div class="col-lg-12">
                            <h2 class="cat-heading d-lg-block text-left">
                                Aaj ki Baat
                            </h2>

                            <div class="row mt-5">
                                <?php
                                $sel_news = dbQuery("SELECT * FROM tabl_post WHERE video!='' OR video_link!='' ORDER BY id DESC LIMIT 4");
                                ?>

                                <div class="col-md-12 col-sm-12">
                                    <div class="row">


                                        <div class="latest-carousel owl-carousel owl-theme">

                                            <?php
                                            // $sel_news = dbQuery("SELECT * FROM tabl_post WHERE video!='' OR video_link!='' ORDER BY headline ASC LIMIT 0,10");
                                            while ($res_news = dbFetchAssoc($sel_news)) {

                                                $cat_details = dbQuery("SELECT tabl_post_to_category.*,tabl_category.category,tabl_category.slug as catslug FROM tabl_post_to_category INNER JOIN tabl_category ON tabl_post_to_category.category_id=tabl_category.id WHERE tabl_post_to_category.post_id='" . $res_news['id'] . "'");
                                                $res_cat_details = dbFetchAssoc($cat_details);
                                            ?>

                                                <a href="<?php echo BASE_URL . '' . $res_cat_details['catslug'] . '/' . $res_news['slug']; ?>.html" title="<?php echo $res_news['headline'] . " : " . $res_news['title']; ?>" class="next-video">
                                                    <span class="thumb">
                                                        <figure>
                                                            <img src="./assets/post/<?php echo $res_news['image']; ?>" alt="<?php echo $res_news['headline'] . " : " . $res_news['title']; ?>" class="lazy" width="298" height="168" style="">
                                                        </figure>
                                                        <span class="icon video-paly"></span>
                                                    </span>
                                                    <p class="caption"><?php echo $res_news['headline'] . " : " . $res_news['title']; ?></p>
                                                </a>
                                            <?php
                                            }
                                            ?>
                                        </div>
                                    </div>

                                </div>
                            </div>
                            <div class="view text-center mt-5">
                                <a href="http://localhost/wintv99_new/category/travells" class="viewall">View All <i class="fa fa-long-arrow-right" aria-hidden="true"></i></a>
                            </div>
                        </div>
                    </div>

                </div>
            </section>

            <section class="bg-accent-dark">

                <div class="container">
                    <div class="row">
                        <div class="col-lg-12">
                            <h2 class="cat-heading d-lg-block text-left">
                                News
                            </h2>

                            <div class="row mt-5">
                                <?php
                                $sel_news = dbQuery("SELECT * FROM tabl_post WHERE video!='' OR video_link!='' ORDER BY id DESC LIMIT 4");
                                ?>

                                <div class="col-md-12 col-sm-12">
                                    <div class="row">


                                        <div class="latest-carousel owl-carousel owl-theme">

                                            <?php
                                            // $sel_news = dbQuery("SELECT * FROM tabl_post WHERE video!='' OR video_link!='' ORDER BY headline ASC LIMIT 0,10");
                                            while ($res_news = dbFetchAssoc($sel_news)) {

                                                $cat_details = dbQuery("SELECT tabl_post_to_category.*,tabl_category.category,tabl_category.slug as catslug FROM tabl_post_to_category INNER JOIN tabl_category ON tabl_post_to_category.category_id=tabl_category.id WHERE tabl_post_to_category.post_id='" . $res_news['id'] . "'");
                                                $res_cat_details = dbFetchAssoc($cat_details);
                                            ?>

                                                <a href="<?php echo BASE_URL . '' . $res_cat_details['catslug'] . '/' . $res_news['slug']; ?>.html" title="<?php echo $res_news['headline'] . " : " . $res_news['title']; ?>" class="next-video">
                                                    <span class="thumb">
                                                        <figure>
                                                            <img src="./assets/post/<?php echo $res_news['image']; ?>" alt="<?php echo $res_news['headline'] . " : " . $res_news['title']; ?>" class="lazy" width="298" height="168" style="">
                                                        </figure>
                                                        <span class="icon video-paly"></span>
                                                    </span>
                                                    <p class="caption"><?php echo $res_news['headline'] . " : " . $res_news['title']; ?></p>
                                                </a>
                                            <?php
                                            }
                                            ?>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="view text-center mt-5">
                        <a href="http://localhost/wintv99_new/category/travells" class="viewall">View All <i class="fa fa-long-arrow-right" aria-hidden="true"></i></a>
                    </div>

                </div>
            </section>

            <section class="bg-accent-dark">

                <div class="container">
                    <div class="row">
                        <div class="col-lg-12">
                            <h2 class="cat-heading d-lg-block text-left">
                                Astrology
                            </h2>

                            <div class="row mt-5">
                                <?php
                                $sel_news = dbQuery("SELECT * FROM tabl_post WHERE video!='' OR video_link!='' ORDER BY id DESC LIMIT 4");
                                ?>

                                <div class="col-md-12 col-sm-12">
                                    <div class="row">


                                        <div class="latest-carousel owl-carousel owl-theme">

                                            <?php
                                            // $sel_news = dbQuery("SELECT * FROM tabl_post WHERE video!='' OR video_link!='' ORDER BY headline ASC LIMIT 0,10");
                                            while ($res_news = dbFetchAssoc($sel_news)) {

                                                $cat_details = dbQuery("SELECT tabl_post_to_category.*,tabl_category.category,tabl_category.slug as catslug FROM tabl_post_to_category INNER JOIN tabl_category ON tabl_post_to_category.category_id=tabl_category.id WHERE tabl_post_to_category.post_id='" . $res_news['id'] . "'");
                                                $res_cat_details = dbFetchAssoc($cat_details);
                                            ?>

                                                <a href="<?php echo BASE_URL . '' . $res_cat_details['catslug'] . '/' . $res_news['slug']; ?>.html" title="<?php echo $res_news['headline'] . " : " . $res_news['title']; ?>" class="next-video">
                                                    <span class="thumb">
                                                        <figure>
                                                            <img src="./assets/post/<?php echo $res_news['image']; ?>" alt="<?php echo $res_news['headline'] . " : " . $res_news['title']; ?>" class="lazy" width="298" height="168" style="">
                                                        </figure>
                                                        <span class="icon video-paly"></span>
                                                    </span>
                                                    <p class="caption"><?php echo $res_news['headline'] . " : " . $res_news['title']; ?></p>
                                                </a>
                                            <?php
                                            }
                                            ?>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="view text-center mt-5">
                        <a href="http://localhost/wintv99_new/category/travells" class="viewall">View All <i class="fa fa-long-arrow-right" aria-hidden="true"></i></a>
                    </div>

                </div>
            </section>

            <section class="bg-accent-dark">

                <div class="container">
                    <div class="row">
                        <div class="col-lg-12">
                            <h2 class="cat-heading d-lg-block text-left">
                                Yoga
                            </h2>

                            <div class="row mt-5">
                                <?php
                                $sel_news = dbQuery("SELECT * FROM tabl_post WHERE video!='' OR video_link!='' ORDER BY id DESC LIMIT 4");
                                ?>

                                <div class="col-md-12 col-sm-12">
                                    <div class="row">


                                        <div class="latest-carousel owl-carousel owl-theme">

                                            <?php
                                            // $sel_news = dbQuery("SELECT * FROM tabl_post WHERE video!='' OR video_link!='' ORDER BY headline ASC LIMIT 0,10");
                                            while ($res_news = dbFetchAssoc($sel_news)) {

                                                $cat_details = dbQuery("SELECT tabl_post_to_category.*,tabl_category.category,tabl_category.slug as catslug FROM tabl_post_to_category INNER JOIN tabl_category ON tabl_post_to_category.category_id=tabl_category.id WHERE tabl_post_to_category.post_id='" . $res_news['id'] . "'");
                                                $res_cat_details = dbFetchAssoc($cat_details);
                                            ?>

                                                <a href="<?php echo BASE_URL . '' . $res_cat_details['catslug'] . '/' . $res_news['slug']; ?>.html" title="<?php echo $res_news['headline'] . " : " . $res_news['title']; ?>" class="next-video">
                                                    <span class="thumb">
                                                        <figure>
                                                            <img src="./assets/post/<?php echo $res_news['image']; ?>" alt="<?php echo $res_news['headline'] . " : " . $res_news['title']; ?>" class="lazy" width="298" height="168" style="">
                                                        </figure>
                                                        <span class="icon video-paly"></span>
                                                    </span>
                                                    <p class="caption"><?php echo $res_news['headline'] . " : " . $res_news['title']; ?></p>
                                                </a>
                                            <?php
                                            }
                                            ?>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="view text-center mt-5">
                        <a href="http://localhost/wintv99_new/category/travells" class="viewall">View All <i class="fa fa-long-arrow-right" aria-hidden="true"></i></a>
                    </div>
                </div>
            </section>

            <section class="bg-accent-dark">

                <div class="container">
                    <div class="row">
                        <div class="col-lg-12">
                            <h2 class="cat-heading d-lg-block text-left">
                                Kurukshetra
                            </h2>

                            <div class="row mt-5">
                                <?php
                                $sel_news = dbQuery("SELECT * FROM tabl_post WHERE video!='' OR video_link!='' ORDER BY id DESC LIMIT 4");
                                ?>

                                <div class="col-md-12 col-sm-12">
                                    <div class="row">


                                        <div class="latest-carousel owl-carousel owl-theme">

                                            <?php
                                            // $sel_news = dbQuery("SELECT * FROM tabl_post WHERE video!='' OR video_link!='' ORDER BY headline ASC LIMIT 0,10");
                                            while ($res_news = dbFetchAssoc($sel_news)) {

                                                $cat_details = dbQuery("SELECT tabl_post_to_category.*,tabl_category.category,tabl_category.slug as catslug FROM tabl_post_to_category INNER JOIN tabl_category ON tabl_post_to_category.category_id=tabl_category.id WHERE tabl_post_to_category.post_id='" . $res_news['id'] . "'");
                                                $res_cat_details = dbFetchAssoc($cat_details);
                                            ?>

                                                <a href="<?php echo BASE_URL . '' . $res_cat_details['catslug'] . '/' . $res_news['slug']; ?>.html" title="<?php echo $res_news['headline'] . " : " . $res_news['title']; ?>" class="next-video">
                                                    <span class="thumb">
                                                        <figure>
                                                            <img src="./assets/post/<?php echo $res_news['image']; ?>" alt="<?php echo $res_news['headline'] . " : " . $res_news['title']; ?>" class="lazy" width="298" height="168" style="">
                                                        </figure>
                                                        <span class="icon video-paly"></span>
                                                    </span>
                                                    <p class="caption"><?php echo $res_news['headline'] . " : " . $res_news['title']; ?></p>
                                                </a>
                                            <?php
                                            }
                                            ?>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="view text-center mt-5">
                        <a href="http://localhost/wintv99_new/category/travells" class="viewall">View All <i class="fa fa-long-arrow-right" aria-hidden="true"></i></a>
                    </div>
                </div>
            </section>

            <section class="bg-accent-dark">

                <div class="container">
                    <div class="row">
                        <div class="col-lg-12">
                            <h2 class="cat-heading d-lg-block text-left">
                                Haqiaqt kya hai
                            </h2>

                            <div class="row mt-5">
                                <?php
                                $sel_news = dbQuery("SELECT * FROM tabl_post WHERE video!='' OR video_link!='' ORDER BY id DESC LIMIT 4");
                                ?>

                                <div class="col-md-12 col-sm-12">
                                    <div class="row">


                                        <div class="latest-carousel owl-carousel owl-theme">

                                            <?php
                                            // $sel_news = dbQuery("SELECT * FROM tabl_post WHERE video!='' OR video_link!='' ORDER BY headline ASC LIMIT 0,10");
                                            while ($res_news = dbFetchAssoc($sel_news)) {

                                                $cat_details = dbQuery("SELECT tabl_post_to_category.*,tabl_category.category,tabl_category.slug as catslug FROM tabl_post_to_category INNER JOIN tabl_category ON tabl_post_to_category.category_id=tabl_category.id WHERE tabl_post_to_category.post_id='" . $res_news['id'] . "'");
                                                $res_cat_details = dbFetchAssoc($cat_details);
                                            ?>

                                                <a href="<?php echo BASE_URL . '' . $res_cat_details['catslug'] . '/' . $res_news['slug']; ?>.html" title="<?php echo $res_news['headline'] . " : " . $res_news['title']; ?>" class="next-video">
                                                    <span class="thumb">
                                                        <figure>
                                                            <img src="./assets/post/<?php echo $res_news['image']; ?>" alt="<?php echo $res_news['headline'] . " : " . $res_news['title']; ?>" class="lazy" width="298" height="168" style="">
                                                        </figure>
                                                        <span class="icon video-paly"></span>
                                                    </span>
                                                    <p class="caption"><?php echo $res_news['headline'] . " : " . $res_news['title']; ?></p>
                                                </a>
                                            <?php
                                            }
                                            ?>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="view text-center mt-5">
                        <a href="http://localhost/wintv99_new/category/travells" class="viewall">View All <i class="fa fa-long-arrow-right" aria-hidden="true"></i></a>
                    </div>
                </div>
            </section>

            <section class="bg-accent-dark">

                <div class="container">
                    <div class="row">
                        <div class="col-lg-12">
                            <h2 class="cat-heading d-lg-block text-left">
                                Muqabala
                            </h2>

                            <div class="row mt-5">
                                <?php
                                $sel_news = dbQuery("SELECT * FROM tabl_post WHERE video!='' OR video_link!='' ORDER BY id DESC LIMIT 4");
                                ?>

                                <div class="col-md-12 col-sm-12">
                                    <div class="row">


                                        <div class="latest-carousel owl-carousel owl-theme">

                                            <?php
                                            // $sel_news = dbQuery("SELECT * FROM tabl_post WHERE video!='' OR video_link!='' ORDER BY headline ASC LIMIT 0,10");
                                            while ($res_news = dbFetchAssoc($sel_news)) {

                                                $cat_details = dbQuery("SELECT tabl_post_to_category.*,tabl_category.category,tabl_category.slug as catslug FROM tabl_post_to_category INNER JOIN tabl_category ON tabl_post_to_category.category_id=tabl_category.id WHERE tabl_post_to_category.post_id='" . $res_news['id'] . "'");
                                                $res_cat_details = dbFetchAssoc($cat_details);
                                            ?>

                                                <a href="<?php echo BASE_URL . '' . $res_cat_details['catslug'] . '/' . $res_news['slug']; ?>.html" title="<?php echo $res_news['headline'] . " : " . $res_news['title']; ?>" class="next-video">
                                                    <span class="thumb">
                                                        <figure>
                                                            <img src="./assets/post/<?php echo $res_news['image']; ?>" alt="<?php echo $res_news['headline'] . " : " . $res_news['title']; ?>" class="lazy" width="298" height="168" style="">
                                                        </figure>
                                                        <span class="icon video-paly"></span>
                                                    </span>
                                                    <p class="caption"><?php echo $res_news['headline'] . " : " . $res_news['title']; ?></p>
                                                </a>
                                            <?php
                                            }
                                            ?>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="view text-center mt-5">
                        <a href="http://localhost/wintv99_new/category/travells" class="viewall">View All <i class="fa fa-long-arrow-right" aria-hidden="true"></i></a>
                    </div>
                </div>
            </section>

            <section class="bg-accent-dark">

                <div class="container">
                    <div class="row">
                        <div class="col-lg-12">
                            <h2 class="cat-heading d-lg-block text-left">
                                Entertainment
                            </h2>

                            <div class="row mt-5">
                                <?php
                                $sel_news = dbQuery("SELECT * FROM tabl_post WHERE video!='' OR video_link!='' ORDER BY id DESC LIMIT 4");
                                ?>

                                <div class="col-md-12 col-sm-12">
                                    <div class="row">


                                        <div class="latest-carousel owl-carousel owl-theme">

                                            <?php
                                            // $sel_news = dbQuery("SELECT * FROM tabl_post WHERE video!='' OR video_link!='' ORDER BY headline ASC LIMIT 0,10");
                                            while ($res_news = dbFetchAssoc($sel_news)) {

                                                $cat_details = dbQuery("SELECT tabl_post_to_category.*,tabl_category.category,tabl_category.slug as catslug FROM tabl_post_to_category INNER JOIN tabl_category ON tabl_post_to_category.category_id=tabl_category.id WHERE tabl_post_to_category.post_id='" . $res_news['id'] . "'");
                                                $res_cat_details = dbFetchAssoc($cat_details);
                                            ?>

                                                <a href="<?php echo BASE_URL . '' . $res_cat_details['catslug'] . '/' . $res_news['slug']; ?>.html" title="<?php echo $res_news['headline'] . " : " . $res_news['title']; ?>" class="next-video">
                                                    <span class="thumb">
                                                        <figure>
                                                            <img src="./assets/post/<?php echo $res_news['image']; ?>" alt="<?php echo $res_news['headline'] . " : " . $res_news['title']; ?>" class="lazy" width="298" height="168" style="">
                                                        </figure>
                                                        <span class="icon video-paly"></span>
                                                    </span>
                                                    <p class="caption"><?php echo $res_news['headline'] . " : " . $res_news['title']; ?></p>
                                                </a>
                                            <?php
                                            }
                                            ?>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="view text-center mt-5">
                        <a href="http://localhost/wintv99_new/category/travells" class="viewall">View All <i class="fa fa-long-arrow-right" aria-hidden="true"></i></a>
                    </div>

                </div>
            </section>

            <section class="bg-accent-dark">

                <div class="container">
                    <div class="row">
                        <div class="col-lg-12">
                            <h2 class="cat-heading d-lg-block text-left">
                                Sports
                            </h2>

                            <div class="row mt-5">
                                <?php
                                $sel_news = dbQuery("SELECT * FROM tabl_post WHERE video!='' OR video_link!='' ORDER BY id DESC LIMIT 4");
                                ?>

                                <div class="col-md-12 col-sm-12">
                                    <div class="row">


                                        <div class="latest-carousel owl-carousel owl-theme">

                                            <?php
                                            // $sel_news = dbQuery("SELECT * FROM tabl_post WHERE video!='' OR video_link!='' ORDER BY headline ASC LIMIT 0,10");
                                            while ($res_news = dbFetchAssoc($sel_news)) {

                                                $cat_details = dbQuery("SELECT tabl_post_to_category.*,tabl_category.category,tabl_category.slug as catslug FROM tabl_post_to_category INNER JOIN tabl_category ON tabl_post_to_category.category_id=tabl_category.id WHERE tabl_post_to_category.post_id='" . $res_news['id'] . "'");
                                                $res_cat_details = dbFetchAssoc($cat_details);
                                            ?>

                                                <a href="<?php echo BASE_URL . '' . $res_cat_details['catslug'] . '/' . $res_news['slug']; ?>.html" title="<?php echo $res_news['headline'] . " : " . $res_news['title']; ?>" class="next-video">
                                                    <span class="thumb">
                                                        <figure>
                                                            <img src="./assets/post/<?php echo $res_news['image']; ?>" alt="<?php echo $res_news['headline'] . " : " . $res_news['title']; ?>" class="lazy" width="298" height="168" style="">
                                                        </figure>
                                                        <span class="icon video-paly"></span>
                                                    </span>
                                                    <p class="caption"><?php echo $res_news['headline'] . " : " . $res_news['title']; ?></p>
                                                </a>
                                            <?php
                                            }
                                            ?>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="view text-center mt-5">
                        <a href="http://localhost/wintv99_new/category/travells" class="viewall">View All <i class="fa fa-long-arrow-right" aria-hidden="true"></i></a>
                    </div>
                </div>
            </section>


            <section class="bg-accent-dark">

                <div class="container">
                    <div class="row">
                        <div class="col-lg-12">
                            <h2 class="cat-heading d-lg-block text-left">
                                Lifestyle
                            </h2>

                            <div class="row mt-5">
                                <?php
                                $sel_news = dbQuery("SELECT * FROM tabl_post WHERE video!='' OR video_link!='' ORDER BY id DESC LIMIT 4");
                                ?>

                                <div class="col-md-12 col-sm-12">
                                    <div class="row">


                                        <div class="latest-carousel owl-carousel owl-theme">

                                            <?php
                                            // $sel_news = dbQuery("SELECT * FROM tabl_post WHERE video!='' OR video_link!='' ORDER BY headline ASC LIMIT 0,10");
                                            while ($res_news = dbFetchAssoc($sel_news)) {

                                                $cat_details = dbQuery("SELECT tabl_post_to_category.*,tabl_category.category,tabl_category.slug as catslug FROM tabl_post_to_category INNER JOIN tabl_category ON tabl_post_to_category.category_id=tabl_category.id WHERE tabl_post_to_category.post_id='" . $res_news['id'] . "'");
                                                $res_cat_details = dbFetchAssoc($cat_details);
                                            ?>

                                                <a href="<?php echo BASE_URL . '' . $res_cat_details['catslug'] . '/' . $res_news['slug']; ?>.html" title="<?php echo $res_news['headline'] . " : " . $res_news['title']; ?>" class="next-video">
                                                    <span class="thumb">
                                                        <figure>
                                                            <img src="./assets/post/<?php echo $res_news['image']; ?>" alt="<?php echo $res_news['headline'] . " : " . $res_news['title']; ?>" class="lazy" width="298" height="168" style="">
                                                        </figure>
                                                        <span class="icon video-paly"></span>
                                                    </span>
                                                    <p class="caption"><?php echo $res_news['headline'] . " : " . $res_news['title']; ?></p>
                                                </a>
                                            <?php
                                            }
                                            ?>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="view text-center mt-5">
                        <a href="http://localhost/wintv99_new/category/travells" class="viewall">View All <i class="fa fa-long-arrow-right" aria-hidden="true"></i></a>
                    </div>

                </div>
            </section>

            <section class="bg-accent-dark">

                <div class="container">
                    <div class="row">
                        <div class="col-lg-12">
                            <h2 class="cat-heading d-lg-block text-left">
                                Originals
                            </h2>

                            <div class="row mt-5">
                                <?php
                                $sel_news = dbQuery("SELECT * FROM tabl_post WHERE video!='' OR video_link!='' ORDER BY id DESC LIMIT 4");
                                ?>

                                <div class="col-md-12 col-sm-12">
                                    <div class="row">


                                        <div class="latest-carousel owl-carousel owl-theme">

                                            <?php
                                            // $sel_news = dbQuery("SELECT * FROM tabl_post WHERE video!='' OR video_link!='' ORDER BY headline ASC LIMIT 0,10");
                                            while ($res_news = dbFetchAssoc($sel_news)) {

                                                $cat_details = dbQuery("SELECT tabl_post_to_category.*,tabl_category.category,tabl_category.slug as catslug FROM tabl_post_to_category INNER JOIN tabl_category ON tabl_post_to_category.category_id=tabl_category.id WHERE tabl_post_to_category.post_id='" . $res_news['id'] . "'");
                                                $res_cat_details = dbFetchAssoc($cat_details);
                                            ?>

                                                <a href="<?php echo BASE_URL . '' . $res_cat_details['catslug'] . '/' . $res_news['slug']; ?>.html" title="<?php echo $res_news['headline'] . " : " . $res_news['title']; ?>" class="next-video">
                                                    <span class="thumb">
                                                        <figure>
                                                            <img src="./assets/post/<?php echo $res_news['image']; ?>" alt="<?php echo $res_news['headline'] . " : " . $res_news['title']; ?>" class="lazy" width="298" height="168" style="">
                                                        </figure>
                                                        <span class="icon video-paly"></span>
                                                    </span>
                                                    <p class="caption"><?php echo $res_news['headline'] . " : " . $res_news['title']; ?></p>
                                                </a>
                                            <?php
                                            }
                                            ?>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </section>
            <?php include("./inc/footer_main.php"); ?>
        </div>


        <!-- jquery-->
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/js/bootstrap.bundle.min.js"
            integrity="sha384-MrcW6ZMFYlzcLA8Nl+NtUVF0sA7MsXsP1UyJoMp4YLEuNSfAP+JcXn/tWtIaxVXM"
            crossorigin="anonymous"></script>
        <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.2/dist/umd/popper.min.js"
            integrity="sha384-IQsoLXl5PILFhosVNubq5LC7Qb9DXgDA9i+tQ8Zj3iwWAwPtgFTxbJ8NT4GN1R8p"
            crossorigin="anonymous"></script>
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/js/bootstrap.min.js"
            integrity="sha384-cVKIPhGWiC2Al4u+LWgxfKTRIcfu0JTxR+EQDz/bgldoEyl4H0zUF0QKbrJ0EcQF"
            crossorigin="anonymous"></script>
        <script src="js/jquery-2.2.4.min.js" type="text/javascript"></script>
        <!-- Plugins js -->
        <script src="js/plugins.js" type="text/javascript"></script>
        <!-- Popper js -->
        <script src="js/popper.js" type="text/javascript"></script>
        <!-- Bootstrap js -->
        <script src="js/bootstrap.min.js" type="text/javascript"></script>
        <!-- WOW JS -->
        <script src="js/wow.min.js"></script>
        <!-- Owl Cauosel JS -->
        <script src="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/owl.carousel.min.js" integrity="sha512-bPs7Ae6pVvhOSiIcyUClR7/q2OAsRiovw4vAkX+zJbw3ShAeeqezq50RIIcIURq7Oa20rW2n2q+fyXBNcU9lrw==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>

        <script>
            $('.latest-carousel').owlCarousel({
                loop: false,
                margin: 10,
                nav: true,
                navText: [
                    '<span class="custom-prev"><i class="fa fa-chevron-left"></i></span>',
                    '<span class="custom-next"><i class="fa fa-chevron-right"></i></span>'
                ],
                dots: false,
                responsive: {
                    0: {
                        items: 1
                    },
                    480: {
                        items: 2
                    },
                    600: {
                        items: 3
                    },
                    1000: {
                        items: 4
                    }
                }
            })
        </script>
        <!-- Meanmenu Js -->
        <script src="js/jquery.meanmenu.min.js" type="text/javascript"></script>
        <!-- Srollup js -->
        <script src="js/jquery.scrollUp.min.js" type="text/javascript"></script>
        <!-- jquery.counterup js -->
        <script src="js/jquery.counterup.min.js"></script>
        <script src="js/waypoints.min.js"></script>
        <!-- Nivo slider js -->
        <script src="vendor/slider/js/jquery.nivo.slider.js" type="text/javascript"></script>
        <script src="vendor/slider/home.js" type="text/javascript"></script>
        <!-- Isotope js -->
        <script src="js/isotope.pkgd.min.js" type="text/javascript"></script>
        <!-- Magnific Popup -->
        <script src="js/jquery.magnific-popup.min.js"></script>
        <!-- Ticker Js -->
        <script src="js/ticker.js" type="text/javascript"></script>
        <!-- Custom Js -->
        <script src="js/main.js" type="text/javascript"></script>

        <script>
            document.addEventListener('DOMContentLoaded', function() {
                const accordionItems = document.querySelectorAll('.accordion-item');

                accordionItems.forEach(function(item) {
                    const title = item.querySelector('.accordion-title');

                    title.addEventListener('click', function() {
                        const content = this.nextElementSibling;

                        if (content.style.display === 'block') {
                            content.style.display = 'none';
                            this.classList.remove('active');
                        } else {
                            content.style.display = 'block';
                            this.classList.add('active');
                        }
                    });
                });
            });
            // here another js
            function toggleAdditionalLinks(event) {
                var additionalLinks = document.getElementById("additionalLinks");
                var additionalLinkstour = document.getElementById("additionalLinks-tour");
                var additionalLinksstay = document.getElementById("additionalLinks-stay");

                // Prevent the click event from propagating to the document
                event.stopPropagation();

                // Hide other sections
                if (additionalLinkstour.style.display === "block") {
                    additionalLinkstour.style.display = "none";
                    document.removeEventListener('click', hideAdditionalLinkstourOnClickOutside);
                }

                if (additionalLinksstay.style.display === "block") {
                    additionalLinksstay.style.display = "none";
                    document.removeEventListener('click', hideAdditionalLinksstayOnClickOutside);
                }

                if (additionalLinks.style.display === "none") {
                    additionalLinks.style.display = "block";
                    document.addEventListener('click', hideAdditionalLinksOnClickOutside);
                } else {
                    additionalLinks.style.display = "none";
                    document.removeEventListener('click', hideAdditionalLinksOnClickOutside);
                }
            }

            function hideAdditionalLinksOnClickOutside(event) {
                var additionalLinks = document.getElementById("additionalLinks");
                if (!additionalLinks.contains(event.target) && event.target.id !== 'toggleLink') {
                    additionalLinks.style.display = "none";
                    document.removeEventListener('click', hideAdditionalLinksOnClickOutside);
                }
            }

            function toggleAdditionalLinkstour(event) {
                var additionalLinkstour = document.getElementById("additionalLinks-tour");
                var additionalLinks = document.getElementById("additionalLinks");
                var additionalLinksstay = document.getElementById("additionalLinks-stay");

                // Prevent the click event from propagating to the document
                event.stopPropagation();

                // Hide other sections
                if (additionalLinks.style.display === "block") {
                    additionalLinks.style.display = "none";
                    document.removeEventListener('click', hideAdditionalLinksOnClickOutside);
                }

                if (additionalLinksstay.style.display === "block") {
                    additionalLinksstay.style.display = "none";
                    document.removeEventListener('click', hideAdditionalLinksstayOnClickOutside);
                }

                if (additionalLinkstour.style.display === "none") {
                    additionalLinkstour.style.display = "block";
                    document.addEventListener('click', hideAdditionalLinkstourOnClickOutside);
                } else {
                    additionalLinkstour.style.display = "none";
                    document.removeEventListener('click', hideAdditionalLinkstourOnClickOutside);
                }
            }

            function hideAdditionalLinkstourOnClickOutside(event) {
                var additionalLinkstour = document.getElementById("additionalLinks-tour");
                if (!additionalLinkstour.contains(event.target) && event.target.id !== 'toggleLinkTour') {
                    additionalLinkstour.style.display = "none";
                    document.removeEventListener('click', hideAdditionalLinkstourOnClickOutside);
                }
            }

            function toggleAdditionalLinksstay(event) {
                var additionalLinksstay = document.getElementById("additionalLinks-stay");
                var additionalLinks = document.getElementById("additionalLinks");
                var additionalLinkstour = document.getElementById("additionalLinks-tour");

                // Prevent the click event from propagating to the document
                event.stopPropagation();

                // Hide other sections
                if (additionalLinks.style.display === "block") {
                    additionalLinks.style.display = "none";
                    document.removeEventListener('click', hideAdditionalLinksOnClickOutside);
                }

                if (additionalLinkstour.style.display === "block") {
                    additionalLinkstour.style.display = "none";
                    document.removeEventListener('click', hideAdditionalLinkstourOnClickOutside);
                }

                if (additionalLinksstay.style.display === "none") {
                    additionalLinksstay.style.display = "block";
                    document.addEventListener('click', hideAdditionalLinksstayOnClickOutside);
                } else {
                    additionalLinksstay.style.display = "none";
                    document.removeEventListener('click', hideAdditionalLinksstayOnClickOutside);
                }
            }

            function hideAdditionalLinksstayOnClickOutside(event) {
                var additionalLinksstay = document.getElementById("additionalLinks-stay");
                if (!additionalLinksstay.contains(event.target) && event.target.id !== 'toggleLinkStay') {
                    additionalLinksstay.style.display = "none";
                    document.removeEventListener('click', hideAdditionalLinksstayOnClickOutside);
                }
            }

            document.getElementById("toggleLink").addEventListener("click", toggleAdditionalLinks);
            document.getElementById("toggleLinkTour").addEventListener("click", toggleAdditionalLinkstour);
            document.getElementById("toggleLinkStay").addEventListener("click", toggleAdditionalLinksstay);


            // Detect scroll event
            window.onscroll = function() {
                scrollFunction()
            };

            function scrollFunction() {
                var footer = document.querySelector('.mobile-footer');
                if (document.body.scrollTop > 20 || document.documentElement.scrollTop > 20) {
                    footer.classList.add('show'); // Add class to show footer
                } else {
                    footer.classList.remove('show'); // Remove class to hide footer
                }
            }
        </script>
        <script>
            function toggleSection(headerElement) {
                const content = headerElement.nextElementSibling;
                const icon = headerElement.querySelector('.icon i');

                if (content.style.display === "block") {
                    content.style.display = "none";
                    icon.classList.remove('fa-chevron-down');
                    icon.classList.add('fa-chevron-up');
                } else {
                    content.style.display = "block";
                    icon.classList.remove('fa-chevron-up');
                    icon.classList.add('fa-chevron-down');
                }
            }


            $(document).ready(function() {
                $('#carouselExampleControls').carousel({
                    interval: 2000 // 2 seconds
                });
            });

            $(document).ready(function() {
                $('#menuIcon').on('click', function() {
                    $(this).toggleClass('bi-list bi-x');
                });

                $('#offcanvasExample').on('hidden.bs.offcanvas', function() {
                    $('#menuIcon').removeClass('bi-x').addClass('bi-list');
                });
            });


            $(document).ready(function() {
                $('#smenuIcon').on('click', function() {
                    $(this).toggleClass('bi-list bi-x');
                });

                $('#offcanvasExample').on('hidden.bs.offcanvas', function() {
                    $('#smenuIcon').removeClass('bi-x').addClass('bi-list');
                });
            });
        </script>
</body>


</html>