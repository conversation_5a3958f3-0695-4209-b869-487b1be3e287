<?php
session_start();
include('lib/db_connection.php');
include('lib/get_functions.php');
include('lib/auth.php');
include('inc/resize-class.php');
$page = 11;
$sub_page = 0;
date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d');
?>
<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <title><?php echo SITE; ?> | Pending Deposits</title>
  <link href="favicon.png" rel="shortcut icon">
  <link href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:400,400i,700,700i,900" rel="stylesheet">
  <!-- VENDORS -->
  <link rel="stylesheet" type="text/css" href="vendors/bootstrap/dist/css/bootstrap.css">
  <link rel="stylesheet" type="text/css" href="vendors/font-feathericons/dist/feather.css">
  <link rel="stylesheet" type="text/css" href="vendors/font-awesome/css/font-awesome.min.css">
  <link rel="stylesheet" type="text/css" href="vendors/font-linearicons/style.css">
  <link rel="stylesheet" type="text/css" href="vendors/font-icomoon/style.css">
  <link rel="stylesheet" type="text/css" href="vendors/perfect-scrollbar/css/perfect-scrollbar.css">
  <link rel="stylesheet" type="text/css" href="vendors/chart.js/dist/Chart.min.css">
  <link rel="stylesheet" type="text/css" href="vendors/jqvmap/dist/jqvmap.min.css">
  <link rel="stylesheet" type="text/css" href="vendors/c3/c3.min.css">
  <link rel="stylesheet" type="text/css"
    href="cdn.datatables.net/v/bs4/dt-1.10.18/fc-3.2.5/r-2.2.2/datatables.min.css" />
  <link rel="stylesheet" type="text/css" href="vendors/tempus-dominus-bs4/build/css/tempusdominus-bootstrap-4.min.css">
  <link rel="stylesheet" type="text/css" href="vendors/fullcalendar/dist/fullcalendar.min.css">
  <link rel="stylesheet" type="text/css" href="vendors/owl.carousel/dist/assets/owl.carousel.min.css">
  <link rel="stylesheet" type="text/css" href="vendors/ionrangeslider/css/ion.rangeSlider.css">
  <link rel="stylesheet" type="text/css" href="vendors/bootstrap-sweetalert/dist/sweetalert.css">
  <link rel="stylesheet" type="text/css" href="vendors/nprogress/nprogress.css">
  <link rel="stylesheet" type="text/css" href="vendors/summernote/dist/summernote.css">
  <link rel="stylesheet" type="text/css" href="vendors/dropify/dist/css/dropify.min.css">
  <link rel="stylesheet" type="text/css" href="vendors/jquery-steps/demo/css/jquery.steps.css">
  <link rel="stylesheet" type="text/css" href="vendors/select2/dist/css/select2.min.css">
  <link rel="stylesheet" type="text/css" href="vendors/bootstrap-select/dist/css/bootstrap-select.min.css">
  <script src="vendors/jquery/dist/jquery.min.js"></script>
  <script src="vendors/popper.js/dist/umd/popper.js"></script>
  <script src="vendors/bootstrap/dist/js/bootstrap.js"></script>
  <script src="vendors/jquery-mousewheel/jquery.mousewheel.min.js"></script>
  <script src="vendors/perfect-scrollbar/js/perfect-scrollbar.jquery.js"></script>
  <script src="vendors/chartist/dist/chartist.min.js"></script>
  <script src="vendors/chart.js/dist/Chart.min.js"></script>
  <script src="vendors/jqvmap/dist/jquery.vmap.min.js"></script>
  <script src="vendors/jqvmap/dist/maps/jquery.vmap.usa.js"></script>
  <script src="vendors/chartist-plugin-tooltip/dist/chartist-plugin-tooltip.min.js"></script>
  <script src="vendors/d3/d3.min.js"></script>
  <script src="vendors/d3-dsv/dist/d3-dsv.js"></script>
  <script src="vendors/d3-time-format/dist/d3-time-format.js"></script>
  <script src="vendors/c3/c3.min.js"></script>
  <script src="vendors/peity/jquery.peity.min.js"></script>
  <script type="text/javascript" src="cdn.datatables.net/v/bs4/dt-1.10.18/fc-3.2.5/r-2.2.2/datatables.min.js"></script>
  <script src="vendors/editable-table/mindmup-editabletable.js"></script>
  <script src="vendors/moment/min/moment.min.js"></script>
  <script src="vendors/tempus-dominus-bs4/build/js/tempusdominus-bootstrap-4.min.js"></script>
  <script src="vendors/fullcalendar/dist/fullcalendar.min.js"></script>
  <script src="vendors/owl.carousel/dist/owl.carousel.min.js"></script>
  <script src="vendors/ionrangeslider/js/ion.rangeSlider.min.js"></script>
  <script src="vendors/remarkable-bootstrap-notify/dist/bootstrap-notify.min.js"></script>
  <script src="vendors/bootstrap-sweetalert/dist/sweetalert.min.js"></script>
  <script src="vendors/nprogress/nprogress.js"></script>
  <script src="vendors/summernote/dist/summernote.min.js"></script>
  <script src="vendors/nestable/jquery.nestable.js"></script>
  <script src="vendors/jquery-typeahead/dist/jquery.typeahead.min.js"></script>
  <script src="vendors/autosize/dist/autosize.min.js"></script>
  <script src="vendors/bootstrap-show-password/dist/bootstrap-show-password.min.js"></script>
  <script src="vendors/dropify/dist/js/dropify.min.js"></script>
  <script src="vendors/html5-form-validation/dist/jquery.validation.min.js"></script>
  <script src="vendors/jquery-steps/build/jquery.steps.min.js"></script>
  <script src="vendors/jquery-mask-plugin/dist/jquery.mask.min.js"></script>
  <script src="vendors/select2/dist/js/select2.full.min.js"></script>
  <script src="vendors/bootstrap-select/dist/js/bootstrap-select.min.js"></script>
  <script src="vendors/d3-dsv/dist/d3-dsv.js"></script>
  <script src="vendors/d3-time-format/dist/d3-time-format.js"></script>
  <script src="vendors/techan/dist/techan.min.js"></script>
  <script src="vendors/Stickyfill/dist/stickyfill.min.js"></script>

  <!-- AIR UI HTML ADMIN TEMPLATE MODULES-->
  <link rel="stylesheet" type="text/css" href="components/vendors/style.css">
  <link rel="stylesheet" type="text/css" href="components/core/style.css">
  <link rel="stylesheet" type="text/css" href="components/widgets/style.css">
  <link rel="stylesheet" type="text/css" href="components/system/style.css">
  <link rel="stylesheet" type="text/css" href="components/menu-left/style.css">
  <link rel="stylesheet" type="text/css" href="components/menu-top/style.css">
  <link rel="stylesheet" type="text/css" href="components/footer/style.css">
  <link rel="stylesheet" type="text/css" href="components/footer-dark/style.css">
  <link rel="stylesheet" type="text/css" href="components/topbar/style.css">
  <link rel="stylesheet" type="text/css" href="components/topbar-dark/style.css">
  <link rel="stylesheet" type="text/css" href="components/subbar/style.css">
  <link rel="stylesheet" type="text/css" href="components/sidebar/style.css">
  <link rel="stylesheet" type="text/css" href="components/chat/style.css">
  <link rel="stylesheet" type="text/css" href="components/apps/style.css">
  <link rel="stylesheet" type="text/css" href="components/apps/style.css">
  <link rel="stylesheet" type="text/css" href="components/extra-apps/style.css">
  <link rel="stylesheet" type="text/css" href="components/ecommerce/style.css">
  <link rel="stylesheet" type="text/css" href="components/dashboards/crypto-terminal/style.css">
  <script src="components/core/index.js"></script>
  <script src="components/menu-left/index.js"></script>
  <script src="components/menu-top/index.js"></script>
  <script src="components/sidebar/index.js"></script>
  <script src="components/topbar/index.js"></script>
  <script src="components/chat/index.js"></script>

  <!-- PRELOADER STYLES-->

</head>

<body class="air__menu--blue air__menu__submenu--blue">
  <div class="air__initialLoading"></div>
  <div class="air__layout">
    <div class="air__menuTop">
      <div class="air__menuTop__outer">
        <div class="air__menuTop__mobileToggleButton air__menuTop__mobileActionToggle"> <span></span> </div>
        <a href="home.php" class="air__menuTop__logo">
          <h1 style="color:#FFF"><?php echo SITE; ?></h1>
        </a>
        <?php include('inc/__menu.php'); ?>
      </div>
    </div>
    <div class="air__menuTop__backdrop air__menuTop__mobileActionToggle"></div>
    <div class="air__layout">
      <?php include('inc/__header.php'); ?>
      <div class="air__layout__content">
        <div class="air__utils__content">
          <div class="air__utils__heading">
            <h5>Pending Deposits: List</h5>
            <nav aria-label="breadcrumb" style="float: right;margin-top: -35px;">
              <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="home.php">Home</a></li>
                <li class="breadcrumb-item active" aria-current="page">Pending Deposits</li>
              </ol>
            </nav>
          </div>
          <div class="row">
            <div class="col-lg-12">
              <div class="card">
                <div class="card-body">
                  <div class="row">
                    <div class="col-md-2">
                      <a href="add_payment_method.php">
                        <button class="btn btn-primary btn-with-addon mr-auto text-nowrap d-none d-md-block"> <span class="btn-addon"> <i class="btn-addon-icon fe fe-plus-circle"></i> </span> Add New </button>
                      </a>
                    </div>
                  </div>
                  <br>
                  <div class="row">
                    <div class="col-lg-12">
                      <div class="mb-5">
                        <div id="example1_wrapper" class="dataTables_wrapper dt-bootstrap4 no-footer">

                          <div class="row">
                            <div class="col-sm-12">
                              <table class="table table-hover nowrap dataTable dtr-inline no-footer" id="example1">
                                <thead>
                                  <tr>
                                    <th>S. No.</th>
                                    <th>Date</th>
                                    <th>Name</th>
                                    <th>Phone No.</th>

                                    <th>Type</th>
                                    <th>Pay Type</th>
                                    <th>Currency</th>
                                    <th>Transaction No.</th>
                                    <th>Recharge Amount</th>
                                    <th>Amount Paid</th>
                                    <th>Amount to pay</th>
                                    <th>Action</th>
                                  </tr>
                                </thead>
                                <tbody>
                                  <?php
                                  $sel = dbQuery("SELECT * FROM tabl_deposits WHERE status=0 ORDER BY id DESC");
                                  $i = 1;
                                  while ($res = dbFetchAssoc($sel)) {
                                    $name = 'NA';
                                    $phone = 'NA';

                                    $query = dbQuery("SELECT * FROM  `tabl_user` WHERE  id =  '" . $res['user_id'] . "' ");
                                    if ($row = dbFetchAssoc($query)) {
                                      $name = $row['name'];
                                      $phone = $row['phone'];
                                    }

                                    $type = "Bank";
                                    $pay_amt = $res['amount'];

                                    $payment_method_id = $res['payment_method'];
                                    $query = dbQuery("SELECT * FROM `tabl_payment_method` WHERE id='$payment_method_id' ORDER BY `id` DESC");

                                    if ($payment_type = dbFetchAssoc($query)) {
                                      $currency_id = $payment_type['currency'];
                                      $currency = getCurrencyData($currency_id);

                                      $currency_name = $currency['currency_name'];
                                      $currency_code = $currency['currency_code'];
                                      $currency_symbol = $currency['currency_symbol'];
                                      $currency_rate = $currency['currency_rate'];

                                      $pay_amt = convertAmount($currency_id, $res['amount']);

                                      if ($payment_type['type'] == 'bank') {
                                        $type = 'Bank';
                                        $pay_amt = $res['amount'];
                                      } else if ($payment_type['type'] == 'online') {
                                        $type = 'Razorpay';
                                        $pay_amt = $res['amount'];
                                      } else {
                                        $type = 'UPI/USDT';
                                      }
                                    }

                                  ?>
                                    <tr>
                                      <td>
                                        <?php echo $i; ?>
                                      </td>
                                      <td>
                                        <?php echo $res['date']; ?>
                                      </td>
                                      <td>
                                        <?php echo $name; ?>
                                      </td>
                                      <td>
                                        <?php echo $phone; ?>
                                      </td>

                                      <td>
                                        <?php echo $type; ?>
                                      </td>
                                      <td>
                                        <?php echo $payment_type['bank_name']; ?>
                                      </td>

                                      <td>
                                        <?php echo $currency_code; ?>
                                      </td>
                                      <td>
                                        <?php echo $res['ref_num']; ?>
                                      </td>

                                      <td>
                                        <?php echo $res['amount']; ?>
                                      </td>

                                      <td>
                                        <?php echo $pay_amt; ?>
                                      </td>


                                      <td>
                                        <input id="amount_<?= $res['id'] ?>" class="form-control"
                                          style="width: 100px"
                                          value="<?= $res['total_amount']; ?>">
                                      </td>

                                      <td>
                                        <a href="javascript:void(0)"
                                          onClick="change_status_deposit('tabl_deposits',1,<?php echo $res['id'] ?>,<?php echo $res['user_id'] ?>)">
                                          <div class="badge badge-success">Accept</div>
                                        </a>
                                        <a href="javascript:void(0)"
                                          onClick="change_status_deposit('tabl_deposits',2,<?php echo $res['id'] ?>,<?php echo $res['user_id'] ?>)">
                                          <div class="badge badge-danger">Reject</div>
                                        </a>
                                      </td>

                                    </tr>
                                  <?php $i++;
                                  } ?>
                                </tbody>
                              </table>
                            </div>
                          </div>

                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <?php include('inc/__footer.php'); ?>
    </div>
  </div>
</body>

</html>
<script>
  ;
  (function($) {
    'use strict'
    $(function() {
      $('#example1').DataTable({
        responsive: true,
      })

      $('#example2').DataTable({
        autoWidth: true,
        scrollX: true,
        fixedColumns: true,
      })

      $('#example3').DataTable({
        autoWidth: true,
        scrollX: true,

        fixedColumns: true,
      })
    })
  })(jQuery)
</script>
<script>
  ;
  (function($) {
    'use strict'
    $(function() {
      $('#form-validation').validate({
        submit: {
          settings: {
            inputContainer: '.form-group',
            errorListClass: 'form-control-error',
            errorClass: 'has-danger',
          },
        },
      })

      $('#form-validation .remove-error').on('click', function() {
        $('#form-validation').removeError()
      })

      $('#form-validation-simple').validate({
        submit: {
          settings: {
            inputContainer: '.form-group',
            errorListClass: 'form-control-error-list',
            errorClass: 'has-danger',
          },
        },
      })

      $('#form-validation-simple .remove-error').on('click', function() {
        $('#form-validation-simple').removeError()
      })

      $('.select2').select2()
    })
  })(jQuery)
</script>
<script>
  function isNumber(evt) {
    var iKeyCode = (evt.which) ? evt.which : evt.keyCode
    if (iKeyCode != 46 && iKeyCode > 31 && (iKeyCode < 48 || iKeyCode > 57))
      return false;

    return true;
  }
</script>
<script>
  function delete_author(id) {
    var retVal = confirm("Are you sure want to delete.");
    if (retVal == true) {
      $.ajax({
        url: 'ajax/delete_author.php',
        type: 'post',
        data: {
          'id': id
        },
        success: function(data) {
          //alert(data);
          if (data == 1) {
            location.reload();
          }
        },
      });
    } else {
      return false;
    }

  }
</script>
<script>
  function change_status(tabl, val, row_id) {
    var retVal = confirm("Are you sure want to change status.");
    if (retVal == true) {
      $.ajax({
        url: 'ajax/activate.php',
        type: 'post',
        data: {
          'tabl': tabl,
          'val': val,
          'row_id': row_id
        },
        success: function(data) {
          //alert(data);
          if (data == 1) {
            location.reload();
          }
        },
      });
    } else {
      return false;
    }


  }
</script>

<script>
  // function change_status(tabl, val, row_id, user_id, amount) {
  function change_status_deposit(tabl, val, row_id, user_id) {
    var amount = document.getElementById("amount_" + row_id).value;
    if (val == 1) {
      var status = 'Accept';
    } else if (val == 2) {
      var status = 'Reject';
    }
    var retVal = confirm("Are you sure want to " + status + " this Recharge.");
    if (retVal == true) {
      $.ajax({
        url: './ajax/recharge.php',
        type: 'post',
        data: {
          'tabl': tabl,
          'val': val,
          'row_id': row_id,
          'user_id': user_id,
          'amount': amount
        },
        success: function(data) {
          //alert(data);
          if (data == 1) {
            // location.reload();
          }
        },
      });
    } else {
      return false;
    }


  }
</script>