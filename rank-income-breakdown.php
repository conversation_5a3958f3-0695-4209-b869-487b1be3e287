<?php
include('./lib/auth.php');
include('lib/translation_helper.php');
date_default_timezone_set("Asia/Kolkata");
$date = date('Y-m-d H:i:s');

// Get user's current rank and details
$user_query = dbQuery("
    SELECT u.current_rank_id, u.name, r.name as rank_name, r.daily_credit
    FROM tabl_user u
    LEFT JOIN tabl_ranks r ON u.current_rank_id = r.id
    WHERE u.id = '$user_id'
");

$user_data = dbFetchAssoc($user_query);
$current_rank_id = $user_data['current_rank_id'] ?: 0;
$current_rank_name = $user_data['rank_name'] ?: 'No Rank';
$user_name = $user_data['name'];

// Calculate current rank daily income only
$current_rank_income = 0;
$income_breakdown = [];

if ($current_rank_id > 0) {
    $rank_query = dbQuery("
        SELECT id, name, daily_credit
        FROM tabl_ranks
        WHERE id = '$current_rank_id'
    ");

    if (dbNumRows($rank_query) > 0) {
        $rank = dbFetchAssoc($rank_query);
        $current_rank_income = floatval($rank['daily_credit']);
        $income_breakdown[] = [
            'name' => $rank['name'],
            'credit' => $current_rank_income
        ];
    }
}

// Get all ranks for reference
$all_ranks_query = dbQuery("SELECT * FROM tabl_ranks ORDER BY id ASC");

// Get monthly statistics for rank income
$monthly_stats_query = dbQuery("
    SELECT 
        YEAR(date) as year,
        MONTH(date) as month,
        COUNT(*) as days_received,
        SUM(amount) as total_amount,
        AVG(amount) as avg_amount
    FROM tabl_walletsummery 
    WHERE user_id = '$user_id' 
    AND actiontype = 'daily_rank_income' 
    AND type = 'credit'
    AND date >= DATE_SUB(CURDATE(), INTERVAL 12 MONTH)
    GROUP BY YEAR(date), MONTH(date)
    ORDER BY year DESC, month DESC
    LIMIT 12
");
?>

<!doctype html>
<html class="no-js" lang="">

<head>
    <base href="<?= BASE_URL; ?>">

    <meta charset="utf-8">
    <meta http-equiv="x-ua-compatible" content="ie=edge">
    <title>Win TV 99</title>
    <meta name="description"
        content="Win TV 99 is your go-to destination for the latest news, updates, and insightful stories from across India and around the globe. We are committed to delivering accurate, reliable, and unbiased journalism that empowers our readers with knowledge and understanding">
    <meta name="keywords" content="Spiritual,travel,story,Innovation,stay,news">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!-- Favicon -->
    <link rel="shortcut icon" type="image/x-icon" href="img/favicon2.png">
    <!-- Normalize CSS -->
    <link rel="stylesheet" href="css/normalize.css">
    <!-- Main CSS -->
    <link rel="stylesheet" href="css/main.css">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" rel="stylesheet"
        integrity="sha384-EVSTQN3/azprG1Anm3QDgpJLIm9Nao0Yz1ztcQTwFspd3yD65VohhpuuCOmLASjC" crossorigin="anonymous">
    <link rel="stylesheet" href="css/bootstrap.min.css">

    <!-- Animate CSS -->
    <link rel="stylesheet" href="css/animate.min.css">
    <!-- Font-awesome CSS-->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="css/font-awesome.min.css">
    <!-- Owl Caousel CSS -->
    <link rel="stylesheet" href="vendor/OwlCarousel/owl.carousel.min.css">
    <link rel="stylesheet" href="vendor/OwlCarousel/owl.theme.default.min.css">
    <!-- Main Menu CSS -->
    <link rel="stylesheet" href="css/meanmenu.min.css">
    <!-- Nivo Slider CSS-->
    <link rel="stylesheet" href="vendor/slider/css/nivo-slider.css" type="text/css" />
    <link rel="stylesheet" href="vendor/slider/css/preview.css" type="text/css" media="screen" />
    <!-- Magnific CSS -->
    <link rel="stylesheet" type="text/css" href="css/magnific-popup.css">
    <!-- Switch Style CSS -->
    <link rel="stylesheet" href="css/hover-min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="style.css">
    <!-- For IE -->
    <link rel="stylesheet" type="text/css" href="css/ie-only.css" />
    <link rel="stylesheet" href="css/me.css">
    <link rel="stylesheet" href="css/custom.css">
    <link rel="stylesheet" href="css/lightfooter.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <!-- Modernizr Js -->
    <script src="js/modernizr-2.8.3.min.js"></script>

    <style>
        .income-summary-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .income-amount {
            font-size: 2.5em;
            font-weight: bold;
        }
        .breakdown-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 10px;
            margin: 5px 0;
        }
        .stats-card {
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
            text-align: center;
        }
        .rank-progression {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
        }
        .rank-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #dee2e6;
        }
        .rank-item:last-child {
            border-bottom: none;
        }
        .current-rank-highlight {
            background: #e3f2fd;
            border-radius: 5px;
            padding: 5px;
            font-weight: bold;
        }
    </style>

</head>

<body>
    <div id="main">

        <div id="wrapper">
            <?php include("./inc/header_mobile.php"); ?>

            <?php include("./inc/header.php"); ?>

            <div class="d-lg-none d-block" style="padding-top: 50px;"> </div>
            <div class="d-none d-lg-block" style="padding-top: 130px;"> </div>

            <section class="container mt-5">
                <h1 class="cat-heading text-left">Rank Income Breakdown</h1>
            </section>

            <section class="section-space-bottom mt-5">
                <div class="container mt-4">
                    <div class="row">
                        <!-- Sidebar Menu -->
                        <?php include_once "./inc/sidebar.php"; ?>

                        <!-- Main Rank Income Content -->
                        <div class="col-md-9">
                            <!-- Current Income Status -->
                            <div class="card shadow-sm border-0 mb-4">
                                <div class="card-header bg-light">
                                    <h2 class="mb-0">Current Daily Income</h2>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="income-summary-card">
                                                <h4><i class="fas fa-trophy"></i> Current Daily Income</h4>
                                                <div class="income-amount">₹<?= number_format($current_rank_income, 2) ?></div>
                                                <p class="mb-2">Based on your current rank: <strong><?= htmlspecialchars($current_rank_name) ?></strong></p>
                                                <small>Income from your current rank only</small>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="stats-card">
                                                <h5>Monthly Potential</h5>
                                                <h3 class="text-success">₹<?= number_format($current_rank_income * 30, 2) ?></h3>
                                                <small>Based on 30 days</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Income Breakdown -->
                            <?php if ($current_rank_id > 0): ?>
                            <div class="card shadow-sm border-0 mb-4">
                                <div class="card-header bg-light">
                                    <h2 class="mb-0">Income Breakdown</h2>
                                </div>
                                <div class="card-body">
                                    <p class="mb-3">Your daily income is based on your current rank only:</p>
                                    <?php foreach ($income_breakdown as $rank_info): ?>
                                        <div class="breakdown-item">
                                            <div class="d-flex justify-content-between">
                                                <span><strong><?= htmlspecialchars($rank_info['name']) ?> Rank</strong></span>
                                                <span><strong>₹<?= number_format($rank_info['credit'], 2) ?></strong></span>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                    <hr>
                                    <div class="d-flex justify-content-between">
                                        <span><strong>Current Rank Daily Income:</strong></span>
                                        <span><strong>₹<?= number_format($current_rank_income, 2) ?></strong></span>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>

                            <!-- Monthly Statistics -->
                            <?php if (dbNumRows($monthly_stats_query) > 0): ?>
                            <div class="card shadow-sm border-0 mb-4">
                                <div class="card-header bg-light">
                                    <h2 class="mb-0">Monthly Statistics</h2>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <?php while ($stat = dbFetchAssoc($monthly_stats_query)): ?>
                                            <div class="col-md-3">
                                                <div class="stats-card">
                                                    <h5><?= date('F Y', mktime(0, 0, 0, $stat['month'], 1, $stat['year'])) ?></h5>
                                                    <div class="text-success">
                                                        <h4>₹<?= number_format($stat['total_amount'], 2) ?></h4>
                                                    </div>
                                                    <small><?= $stat['days_received'] ?> days received</small><br>
                                                    <small>Avg: ₹<?= number_format($stat['avg_amount'], 2) ?>/day</small>
                                                </div>
                                            </div>
                                        <?php endwhile; ?>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>

                            <!-- Rank Progression & Income Potential -->
                            <div class="card shadow-sm border-0 mb-4">
                                <div class="card-header bg-light">
                                    <h2 class="mb-0">Rank Progression & Income Potential</h2>
                                </div>
                                <div class="card-body">
                                    <div class="rank-progression">
                                        <?php
                                        while ($rank = dbFetchAssoc($all_ranks_query)):
                                            $is_current = ($rank['id'] == $current_rank_id);
                                            $is_achieved = ($rank['id'] <= $current_rank_id);
                                        ?>
                                            <div class="rank-item <?= $is_current ? 'current-rank-highlight' : '' ?>">
                                                <div>
                                                    <strong><?= htmlspecialchars($rank['name']) ?></strong>
                                                    <?php if ($is_current): ?>
                                                        <span class="badge bg-primary ms-2">Current</span>
                                                    <?php elseif ($is_achieved): ?>
                                                        <span class="badge bg-success ms-2">Achieved</span>
                                                    <?php endif; ?>
                                                    <br>
                                                    <small class="text-muted">Daily Income: ₹<?= number_format($rank['daily_credit'], 2) ?>/day</small>
                                                </div>
                                                <div class="text-end">
                                                    <h6 class="mb-0">₹<?= number_format($rank['daily_credit'], 2) ?></h6>
                                                    <small class="text-muted">Rank Income</small>
                                                </div>
                                            </div>
                                        <?php endwhile; ?>
                                    </div>

                                    <div class="alert alert-info mt-3">
                                        <i class="fas fa-lightbulb"></i>
                                        <strong>How it works:</strong> You receive daily income based on your current rank only.
                                        Higher ranks provide better daily income rates!
                                    </div>
                                </div>
                            </div>

                            <!-- Action Buttons -->
                            <div class="text-center mt-4">
                                <!-- <a href="my-rank.php" class="btn btn-primary btn-lg me-3">
                                    <i class="fas fa-trophy"></i> View My Rank
                                </a> -->
                                <a href="daily-income.php" class="btn btn-success btn-lg me-3">
                                    <i class="fas fa-coins"></i> Daily Income History
                                </a>
                                <a href="wallet-history.php" class="btn btn-info btn-lg">
                                    <i class="fas fa-wallet"></i> Wallet History
                                </a>
                            </div>

                        </div>
                    </div>
                </div>


            </section>

            <?php include("./inc/footer_main.php"); ?>


        </div>


        <!-- jquery-->
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/js/bootstrap.bundle.min.js"
            integrity="sha384-MrcW6ZMFYlzcLA8Nl+NtUVF0sA7MsXsP1UyJoMp4YLEuNSfAP+JcXn/tWtIaxVXM"
            crossorigin="anonymous"></script>
        <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.2/dist/umd/popper.min.js"
            integrity="sha384-IQsoLXl5PILFhosVNubq5LC7Qb9DXgDA9i+tQ8Zj3iwWAwPtgFTxbJ8NT4GN1R8p"
            crossorigin="anonymous"></script>
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/js/bootstrap.min.js"
            integrity="sha384-cVKIPhGWiC2Al4u+LWgxfKTRIcfu0JTxR+EQDz/bgldoEyl4H0zUF0QKbrJ0EcQF"
            crossorigin="anonymous"></script>
        <script src="js/jquery-2.2.4.min.js" type="text/javascript"></script>
        <!-- Plugins js -->
        <script src="js/plugins.js" type="text/javascript"></script>
        <!-- Popper js -->
        <script src="js/popper.js" type="text/javascript"></script>
        <!-- Bootstrap js -->
        <script src="js/bootstrap.min.js" type="text/javascript"></script>
        <!-- WOW JS -->
        <script src="js/wow.min.js"></script>
        <!-- Owl Cauosel JS -->
        <script src="vendor/OwlCarousel/owl.carousel.min.js" type="text/javascript"></script>
        <!-- Meanmenu Js -->
        <script src="js/jquery.meanmenu.min.js" type="text/javascript"></script>
        <!-- Srollup js -->
        <script src="js/jquery.scrollUp.min.js" type="text/javascript"></script>
        <!-- jquery.counterup js -->
        <script src="js/jquery.counterup.min.js"></script>
        <script src="js/waypoints.min.js"></script>
        <!-- Nivo slider js -->
        <script src="vendor/slider/js/jquery.nivo.slider.js" type="text/javascript"></script>
        <script src="vendor/slider/home.js" type="text/javascript"></script>
        <!-- Isotope js -->
        <script src="js/isotope.pkgd.min.js" type="text/javascript"></script>
        <!-- Magnific Popup -->
        <script src="js/jquery.magnific-popup.min.js"></script>
        <!-- Ticker Js -->
        <script src="js/ticker.js" type="text/javascript"></script>
        <!-- Custom Js -->
        <script src="js/main.js" type="text/javascript"></script>

        <script>
            function copyTextToClipboard(owncode) {

                var link = "<?= BASE_URL; ?>signup.php?invite_code=" + owncode;

                navigator.clipboard.writeText(link).then(() => {
                    alert("Referral code copied to clipboard!");
                });
            }
        </script>

        <script>
            document.addEventListener('DOMContentLoaded', function() {
                const accordionItems = document.querySelectorAll('.accordion-item');

                accordionItems.forEach(function(item) {
                    const title = item.querySelector('.accordion-title');

                    title.addEventListener('click', function() {
                        const content = this.nextElementSibling;

                        if (content.style.display === 'block') {
                            content.style.display = 'none';
                            this.classList.remove('active');
                        } else {
                            content.style.display = 'block';
                            this.classList.add('active');
                        }
                    });
                });
            });



            //         function toggleAdditionalLinks() {
            //             var additionalLinks = document.getElementById("additionalLinks");



            //             if (additionalLinks.style.display === "none") {
            //                 additionalLinks.style.display = "block";
            //             } else {
            //                 additionalLinks.style.display = "none";
            //             }
            //  }


            // here another js
            function toggleAdditionalLinks(event) {
                var additionalLinks = document.getElementById("additionalLinks");
                var additionalLinkstour = document.getElementById("additionalLinks-tour");
                var additionalLinksstay = document.getElementById("additionalLinks-stay");

                // Prevent the click event from propagating to the document
                event.stopPropagation();

                // Hide other sections
                if (additionalLinkstour.style.display === "block") {
                    additionalLinkstour.style.display = "none";
                    document.removeEventListener('click', hideAdditionalLinkstourOnClickOutside);
                }

                if (additionalLinksstay.style.display === "block") {
                    additionalLinksstay.style.display = "none";
                    document.removeEventListener('click', hideAdditionalLinksstayOnClickOutside);
                }

                if (additionalLinks.style.display === "none") {
                    additionalLinks.style.display = "block";
                    document.addEventListener('click', hideAdditionalLinksOnClickOutside);
                } else {
                    additionalLinks.style.display = "none";
                    document.removeEventListener('click', hideAdditionalLinksOnClickOutside);
                }
            }

            function hideAdditionalLinksOnClickOutside(event) {
                var additionalLinks = document.getElementById("additionalLinks");
                if (!additionalLinks.contains(event.target) && event.target.id !== 'toggleLink') {
                    additionalLinks.style.display = "none";
                    document.removeEventListener('click', hideAdditionalLinksOnClickOutside);
                }
            }

            function toggleAdditionalLinkstour(event) {
                var additionalLinkstour = document.getElementById("additionalLinks-tour");
                var additionalLinks = document.getElementById("additionalLinks");
                var additionalLinksstay = document.getElementById("additionalLinks-stay");

                // Prevent the click event from propagating to the document
                event.stopPropagation();

                // Hide other sections
                if (additionalLinks.style.display === "block") {
                    additionalLinks.style.display = "none";
                    document.removeEventListener('click', hideAdditionalLinksOnClickOutside);
                }

                if (additionalLinksstay.style.display === "block") {
                    additionalLinksstay.style.display = "none";
                    document.removeEventListener('click', hideAdditionalLinksstayOnClickOutside);
                }

                if (additionalLinkstour.style.display === "none") {
                    additionalLinkstour.style.display = "block";
                    document.addEventListener('click', hideAdditionalLinkstourOnClickOutside);
                } else {
                    additionalLinkstour.style.display = "none";
                    document.removeEventListener('click', hideAdditionalLinkstourOnClickOutside);
                }
            }

            function hideAdditionalLinkstourOnClickOutside(event) {
                var additionalLinkstour = document.getElementById("additionalLinks-tour");
                if (!additionalLinkstour.contains(event.target) && event.target.id !== 'toggleLinkTour') {
                    additionalLinkstour.style.display = "none";
                    document.removeEventListener('click', hideAdditionalLinkstourOnClickOutside);
                }
            }

            function toggleAdditionalLinksstay(event) {
                var additionalLinksstay = document.getElementById("additionalLinks-stay");
                var additionalLinks = document.getElementById("additionalLinks");
                var additionalLinkstour = document.getElementById("additionalLinks-tour");

                // Prevent the click event from propagating to the document
                event.stopPropagation();

                // Hide other sections
                if (additionalLinks.style.display === "block") {
                    additionalLinks.style.display = "none";
                    document.removeEventListener('click', hideAdditionalLinksOnClickOutside);
                }

                if (additionalLinkstour.style.display === "block") {
                    additionalLinkstour.style.display = "none";
                    document.removeEventListener('click', hideAdditionalLinkstourOnClickOutside);
                }

                if (additionalLinksstay.style.display === "none") {
                    additionalLinksstay.style.display = "block";
                    document.addEventListener('click', hideAdditionalLinksstayOnClickOutside);
                } else {
                    additionalLinksstay.style.display = "none";
                    document.removeEventListener('click', hideAdditionalLinksstayOnClickOutside);
                }
            }

            function hideAdditionalLinksstayOnClickOutside(event) {
                var additionalLinksstay = document.getElementById("additionalLinks-stay");
                if (!additionalLinksstay.contains(event.target) && event.target.id !== 'toggleLinkStay') {
                    additionalLinksstay.style.display = "none";
                    document.removeEventListener('click', hideAdditionalLinksstayOnClickOutside);
                }
            }

            document.getElementById("toggleLink").addEventListener("click", toggleAdditionalLinks);
            document.getElementById("toggleLinkTour").addEventListener("click", toggleAdditionalLinkstour);
            document.getElementById("toggleLinkStay").addEventListener("click", toggleAdditionalLinksstay);











            // Detect scroll event
            window.onscroll = function() {
                scrollFunction()
            };

            function scrollFunction() {
                var footer = document.querySelector('.mobile-footer');
                if (document.body.scrollTop > 20 || document.documentElement.scrollTop > 20) {
                    footer.classList.add('show'); // Add class to show footer
                } else {
                    footer.classList.remove('show'); // Remove class to hide footer
                }
            }
        </script>
        <script>
            function toggleSection(headerElement) {
                const content = headerElement.nextElementSibling;
                const icon = headerElement.querySelector('.icon i');

                if (content.style.display === "block") {
                    content.style.display = "none";
                    icon.classList.remove('fa-chevron-down');
                    icon.classList.add('fa-chevron-up');
                } else {
                    content.style.display = "block";
                    icon.classList.remove('fa-chevron-up');
                    icon.classList.add('fa-chevron-down');
                }
            }


            $(document).ready(function() {
                $('#carouselExampleControls').carousel({
                    interval: 2000 // 2 seconds
                });
            });

            $(document).ready(function() {
                $('#menuIcon').on('click', function() {
                    $(this).toggleClass('bi-list bi-x');
                });

                $('#offcanvasExample').on('hidden.bs.offcanvas', function() {
                    $('#menuIcon').removeClass('bi-x').addClass('bi-list');
                });
            });


            $(document).ready(function() {
                $('#smenuIcon').on('click', function() {
                    $(this).toggleClass('bi-list bi-x');
                });

                $('#offcanvasExample').on('hidden.bs.offcanvas', function() {
                    $('#smenuIcon').removeClass('bi-x').addClass('bi-list');
                });
            });
        </script>
</body>


</html>
